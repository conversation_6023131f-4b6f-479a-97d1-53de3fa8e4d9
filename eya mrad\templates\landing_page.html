<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Tunisie Telecom - Prédiction Client</title>
  <style>
    body {
      font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: #181c24;
      display: flex;
      flex-direction: column;
      position: relative;
      transition: background 0.3s;
      padding-bottom: 60px; 
    }
    .top-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 120;
    }
    .mode-toggle {
      background: rgba(0,191,255,0.12);
      border: none;
      border-radius: 50%;
      width: 42px;
      height: 42px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: #00bfff;
      transition: background 0.2s, transform 0.2s;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(0,191,255,0.2);
    }
    .mode-toggle:hover {
      background: rgba(0,191,255,0.22);
      transform: scale(1.1);
    }
    .logout-btn {
      background: rgba(255,71,87,0.12);
      border: 1px solid rgba(255,71,87,0.2);
      border-radius: 50%;
      width: 42px;
      height: 42px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: #ff4757;
      transition: background 0.2s, transform 0.2s;
      backdrop-filter: blur(10px);
      font-weight: 600;
    }
    .logout-btn:hover {
      background: rgba(255,71,87,0.22);
      transform: scale(1.1);
    }
    .landing-container {
      background: rgba(24,28,36,0.92);
      border-radius: 22px;
      box-shadow: 0 12px 40px #0099cc22, 0 1.5px 8px #0099cc22;
      width: 440px;
      margin: 80px auto 40px auto;
      padding: 48px 38px 38px 38px;
      display: flex;
      flex-direction: column;
      align-items: center;
      backdrop-filter: blur(7px) saturate(1.2);
      border: 1.5px solid #00bfff44;
      animation: fadeIn 1.2s cubic-bezier(.4,0,.2,1);
      position: relative;
      z-index: 2;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(40px); }
      to { opacity: 1; transform: none; }
    }
    .logo {
      width: 100px;
      height: 80px;
      border-radius: 50%;
      margin-bottom: 16px;
      filter: drop-shadow(0 2px 8px #0099cc33);
    }
    h2 {
      margin: 0 0 10px 0;
      font-size: 28px;
      color: #00bfff;
      font-weight: 900;
      letter-spacing: 1px;
      text-align: center;
    }
    .subtitle {
      color: #0099cc;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 28px;
      text-align: center;
    }
    .welcome {
      color: #fff;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 18px;
      text-align: center;
      letter-spacing: 0.2px;
    }
    .form-section {
      width: 100%;
      margin-top: 18px;
      margin-bottom: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 18px;
    }
    .form-section select, .form-section input {
      width: 100%;
      padding: 12px 14px;
      border-radius: 10px;
      border: 1.5px solid #00bfff44;
      background: rgba(255,255,255,0.08);
      color: #fff;
      font-size: 16px;
      margin-bottom: 8px;
      outline: none;
      transition: border 0.2s;
      box-sizing: border-box;
    }
    .form-section select:focus, .form-section input:focus {
      border: 1.5px solid #00bfff;
    }
    .form-section label {
      color: #b3e0ff;
      font-size: 15px;
      margin-bottom: 2px;
      font-weight: 600;
      align-self: flex-start;
    }
    .form-section button {
      width: 100%;
      padding: 13px 0;
      border-radius: 10px;
      background: linear-gradient(90deg, #0099cc 0%, #00bfff 100%);
      color: #fff;
      font-size: 17px;
      font-weight: 700;
      border: none;
      box-shadow: 0 4px 16px #0099cc22;
      letter-spacing: 0.5px;
      cursor: pointer;
      transition: background 0.3s, box-shadow 0.3s, transform 0.2s;
    }
    .form-section button:hover {
      background: linear-gradient(90deg, #00bfff 0%, #0099cc 100%);
      transform: translateY(-2px) scale(1.03);
      box-shadow: 0 8px 24px #0099cc33;
    }
    .result {
      margin-top: 18px;
      padding: 18px 16px;
      border-radius: 12px;
      background: rgba(0,191,255,0.08);
      color: #fff;
      font-size: 17px;
      font-weight: 600;
      text-align: center;
      min-height: 40px;
      box-shadow: 0 2px 12px #00bfff22;
      display: none;
    }
    .footer-app {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      color: #00bfff;
      font-size: 14px;
      text-align: center;
      letter-spacing: 0.5px;
      user-select: none;
      background: rgba(24,28,36,0.95);
      padding: 12px 0;
      z-index: 100;
      border-top: 1px solid #00bfff22;
      box-shadow: 0 -2px 20px #00bfff22;
      backdrop-filter: blur(10px);
    }
    
    /* Light mode styles - Fixed visibility issues */
    .light-mode {
      background: #eaf6fb !important;
      color: #222 !important;
    }
    .light-mode .landing-container {
      background: rgba(255,255,255,0.95) !important;
      border: 1.5px solid #b3e0ff !important;
    }
    .light-mode .welcome {
      color: #1a1a1a !important;
    }
    .light-mode .result {
      color: #1a1a1a !important;
      background: rgba(0,153,204,0.1) !important;
    }
    .light-mode .footer-app {
      background: rgba(234,246,251,0.95) !important;
      color: #0099cc !important;
      border-top: 1px solid #b3e0ff !important;
      box-shadow: 0 -2px 20px #b3e0ff22 !important;
    }
    .light-mode .logout-btn {
      color: #d63031 !important;
      background: rgba(214,48,49,0.12) !important;
      border: 1px solid rgba(214,48,49,0.2) !important;
    }
    .light-mode .logout-btn:hover {
      background: rgba(214,48,49,0.22) !important;
    }
    .light-mode h2 {
      color: #004080 !important;
    }
    .light-mode .subtitle {
      color: #0066a3 !important;
    }
    .light-mode .form-section label {
      color: #004080 !important;
    }
    .light-mode .form-section select, 
    .light-mode .form-section input {
      background: rgba(255,255,255,0.8) !important;
      color: #1a1a1a !important;
      border: 1.5px solid #b3e0ff !important;
    }
    .light-mode .form-section select:focus, 
    .light-mode .form-section input:focus {
      border: 1.5px solid #0099cc !important;
    }
    .light-mode .form-section select option {
      background: #ffffff !important;
      color: #1a1a1a !important;
    }
    
    #particles {
      position: fixed;
      left: 0; 
      top: 0; 
      width: 100vw; 
      height: 100vh;
      z-index: 0;
      pointer-events: none;
    }
    
    @media (max-width: 600px) {
      .landing-container { 
        width: 97vw; 
        padding: 24px 4vw; 
        margin: 60px auto 40px auto;
      }
      .top-controls {
        top: 10px;
        right: 10px;
      }
      body {
        padding-bottom: 70px;
      }
    }
  </style>
</head>
<body>
  <div class="top-controls">
    <button class="logout-btn" id="logoutBtn" title="Se déconnecter">⛔</button>
    <button class="mode-toggle" id="modeToggle" title="Basculer mode clair/sombre">🌙</button>
  </div>
  <div id="particles"></div>
  <div class="landing-container">
    <img src="static/logo.png" alt="Tunisie Telecom Logo" class="logo" />
    <h2>Bienvenue</h2>
    <div class="subtitle">Plateforme IA Tunisie Telecom</div>
    <div class="welcome">Bienvenue sur la plateforme de scoring client.<br>Sélectionnez le type de client et renseignez les informations :</div>
    <div class="form-section">
      <label for="typeSelect">Type de client</label>
      <select id="typeSelect">
        <option value="b2c">B2C (Particulier)</option>
        <option value="b2b">B2B (Entreprise)</option>
      </select>
      <div id="formFields">
        <label for="DLMP">DLMP</label>
        <input type="number" id="DLMP" placeholder="DLMP" step="any" required>
        <label for="IMPAYE">IMPAYE</label>
        <input type="number" id="IMPAYE" placeholder="IMPAYE" step="any" required>
        <label for="FRAUDE">FRAUDE</label>
        <input type="number" id="FRAUDE" placeholder="FRAUDE" step="any" required>
        <label for="CTX">CTX</label>
        <input type="number" id="CTX" placeholder="CTX" step="any" required>
      </div>
      <button id="predictBtn">Prédire</button>
      <div class="result" id="resultBox"></div>
    </div>
  </div>
  
  

  <script>
    // Floating particles animation
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      if (!particlesContainer) return;
      particlesContainer.innerHTML = '';
      const particleCount = 30;
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.top = Math.random() * 100 + 'vh';
        particle.style.width = particle.style.height = (Math.random() * 6 + 4) + 'px';
        particle.style.borderRadius = '50%';
        particle.style.background = ['#00bfff', '#0099cc', '#2c0ff8'][Math.floor(Math.random()*3)];
        particle.style.opacity = 0.18 + Math.random() * 0.18;
        particle.style.zIndex = 1;
        particle.style.filter = 'blur(1px)';
        particle.animate([
          { transform: `translateY(0px)` },
          { transform: `translateY(-60vh)` }
        ], {
          duration: 12000 + Math.random()*8000,
          iterations: Infinity,
          direction: 'alternate',
          easing: 'ease-in-out',
          delay: Math.random()*4000
        });
        particlesContainer.appendChild(particle);
      }
    }
    createParticles();
    window.addEventListener('resize', createParticles);

   // commentaire l kol score
    const enriched = {
      1: "Bon client : aucune action requise pour ce client.",
      2: "Client normal : surveiller périodiquement.",
      3: "Client à risque : recommander une relance ou une offre adaptée.",
      4: "Client à haut risque : action urgente recommandée (contact, recouvrement, etc).",
      5: "Blacklisted : à blacklister ou à surveiller de très près."
    };
    
    // Light/dark mode toggle
    const modeToggle = document.getElementById('modeToggle');
    const logoutBtn = document.getElementById('logoutBtn');
    let dark = true;
    
    modeToggle.onclick = function() {
      dark = !dark;
      document.body.classList.toggle('light-mode', !dark);
      modeToggle.textContent = dark ? '🌙' : '☀️';
    };

    // Logout functionality
    logoutBtn.onclick = function() {
      if(confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        // Redirect to login page
        window.location.href = '/login';
        // Alternative: window.location.href = 'login.html';
      }
    };

    document.getElementById('predictBtn').onclick = async function() {
      const type = document.getElementById('typeSelect').value;
      const DLMP = document.getElementById('DLMP').value;
      const IMPAYE = document.getElementById('IMPAYE').value;
      const FRAUDE = document.getElementById('FRAUDE').value;
      const CTX = document.getElementById('CTX').value;
      const resultBox = document.getElementById('resultBox');
      resultBox.style.display = 'none';
      resultBox.textContent = '';
      if(!DLMP || !IMPAYE || !FRAUDE || !CTX) {
        resultBox.textContent = 'Veuillez remplir tous les champs.';
        resultBox.style.display = 'block';
        return;
      }
      let url = type === 'b2b' ? '/predict_b2b' : '/predict_b2c';
      resultBox.textContent = 'Prédiction en cours...';
      resultBox.style.display = 'block';
      try {
        const resp = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ DLMP, IMPAYE, FRAUDE, CTX })
        });
        const data = await resp.json();
        let msg = `<b>Score : ${data.score}</b><br>${data.interpretation}<br><span style='color:#00bfff'>${enriched[data.score]||''}</span>`;
        resultBox.innerHTML = msg;
      } catch(e) {
        resultBox.textContent = 'Erreur lors de la prédiction.';
      }
      resultBox.style.display = 'block';
    };
  </script>
</body>
<div class="footer-app">
    © Réalisé par Eya Mrad dans le cadre d'un STAGE à TUNISIE TELECOM Scoring Client © Tous droits réservés
  </div>
</html>