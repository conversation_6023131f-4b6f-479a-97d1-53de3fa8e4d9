{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d3b732ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    DLMP    ARPU  IMPAYE  FRAUDE  CTX  Cluster\n", "0  121.0  13.795       0       0    0        2\n", "1   21.0  17.245       0       0    0        2\n", "2    6.0   9.775       1       0    0        2\n", "3  -12.0  43.918       0       0    0        0\n", "4  535.0  23.573       0       0    0        1\n"]}], "source": ["import pandas as pd\n", "df_b2c= pd.read_csv(r'C:\\Users\\<USER>\\df_b2c.csv')  \n", "\n", "print(df_b2c.head())"]}, {"cell_type": "code", "execution_count": 2, "id": "d807eb10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['DLMP', 'ARPU', 'IMPAYE', 'FRAUDE', 'CTX', 'Cluster'], dtype='object')\n"]}], "source": ["print(df_b2c.columns)"]}, {"cell_type": "code", "execution_count": 3, "id": "51a347ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Corrélation de chaque variable avec le Cluster :\n", "Cluster    1.000000\n", "DLMP       0.253573\n", "CTX        0.169804\n", "ARPU       0.008373\n", "IMPAYE     0.004832\n", "FRAUDE     0.003289\n", "Name: Cluster, dtype: float64\n"]}], "source": ["corr_with_target = df_b2c.corr(numeric_only=True)['Cluster'].sort_values(ascending=False)\n", "print(\"📊 Corrélation de chaque variable avec le Cluster :\")\n", "print(corr_with_target)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "1c2896ab", "metadata": {}, "outputs": [], "source": ["X = df_b2c[['DLMP', 'IMPAYE', 'FRAUDE', 'CTX']]  # les features\n", "y = df_b2c['Cluster']  # la cible à prédire"]}, {"cell_type": "code", "execution_count": 5, "id": "2e8dbe07", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 6, "id": "f953104d", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [20:37:15] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📘 XGBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.63      0.79      0.70     99477\n", "           1       0.97      0.98      0.97     15461\n", "           2       0.60      0.57      0.58     68800\n", "           3       0.46      0.02      0.04     29488\n", "           4       0.78      0.95      0.86     21827\n", "\n", "    accuracy                           0.66    235053\n", "   macro avg       0.69      0.66      0.63    235053\n", "weighted avg       0.64      0.66      0.62    235053\n", "\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.009464 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 940210, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.860224\n", "[LightGBM] [Info] Start training from score -2.715620\n", "[LightGBM] [Info] Start training from score -1.227160\n", "[LightGBM] [Info] Start training from score -2.076487\n", "[LightGBM] [Info] Start training from score -2.382981\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "📗 LightGBM Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.63      0.79      0.70     99477\n", "           1       0.97      0.98      0.97     15461\n", "           2       0.60      0.57      0.58     68800\n", "           3       0.44      0.03      0.05     29488\n", "           4       0.77      0.96      0.86     21827\n", "\n", "    accuracy                           0.66    235053\n", "   macro avg       0.68      0.67      0.63    235053\n", "weighted avg       0.63      0.66      0.62    235053\n", "\n", "📙 CatBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.62      0.80      0.70     99477\n", "           1       0.97      0.98      0.97     15461\n", "           2       0.60      0.56      0.58     68800\n", "           3       0.28      0.00      0.01     29488\n", "           4       0.77      0.96      0.86     21827\n", "\n", "    accuracy                           0.66    235053\n", "   macro avg       0.65      0.66      0.62    235053\n", "weighted avg       0.61      0.66      0.61    235053\n", "\n", "📕 Random Forest Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.61      0.84      0.70     99477\n", "           1       0.97      0.98      0.97     15461\n", "           2       0.62      0.50      0.56     68800\n", "           3       0.54      0.00      0.01     29488\n", "           4       0.78      0.96      0.86     21827\n", "\n", "    accuracy                           0.65    235053\n", "   macro avg       0.70      0.66      0.62    235053\n", "weighted avg       0.64      0.65      0.61    235053\n", "\n", "\n", "📊 Comparaison des modèles :\n", "           Model  F1_macro  Accuracy\n", "1       LightGBM  0.632473  0.658877\n", "0        XGBoost  0.632409  0.659158\n", "2       CatBoost  0.623497  0.657179\n", "3  Random Forest  0.620066  0.654776\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score, classification_report\n", "from xgboost import XGBClassifier\n", "from lightgbm import LGBMClassifier\n", "from catboost import CatBoostClassifier, Pool\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "# ------------------------------\n", "# Données déjà préparées\n", "# ------------------------------\n", "# X_train, X_test, y_train, y_test doivent déjà être définis\n", "\n", "# Résultats\n", "results = []\n", "\n", "# ------------------------------\n", "# 1. XGBoost\n", "# ------------------------------\n", "xgb = XGBClassifier(\n", "    objective='multi:softprob',\n", "    num_class=5,\n", "    use_label_encoder=False,\n", "    eval_metric='mlogloss',\n", "    learning_rate=0.1,\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42\n", ")\n", "xgb.fit(X_train, y_train)  # sans sample_weight\n", "y_pred_xgb = xgb.predict(X_test)\n", "results.append({\n", "    'Model': 'XGBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_xgb, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_xgb)\n", "})\n", "print(\"📘 XGBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_xgb))\n", "\n", "# ------------------------------\n", "# 2. LightGBM\n", "# ------------------------------\n", "lgbm = LGBMClassifier(\n", "    objective='multiclass',\n", "    num_class=5,\n", "    learning_rate=0.1,\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42\n", ")\n", "lgbm.fit(X_train, y_train)  # sans sample_weight\n", "y_pred_lgbm = lgbm.predict(X_test)\n", "results.append({\n", "    'Model': 'LightGBM',\n", "    'F1_macro': f1_score(y_test, y_pred_lgbm, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_lgbm)\n", "})\n", "print(\"📗 LightGBM Classification Report:\")\n", "print(classification_report(y_test, y_pred_lgbm))\n", "\n", "# ------------------------------\n", "# 3. <PERSON><PERSON><PERSON><PERSON>\n", "# ------------------------------\n", "catboost = CatBoostClassifier(\n", "    iterations=100,\n", "    learning_rate=0.1,\n", "    depth=6,\n", "    loss_function='MultiClass',\n", "    random_seed=42,\n", "    verbose=0\n", ")\n", "catboost.fit(X_train, y_train)  # sans weights\n", "y_pred_cat = catboost.predict(X_test).flatten()\n", "results.append({\n", "    'Model': 'CatBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_cat, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_cat)\n", "})\n", "print(\"📙 CatBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_cat))\n", "\n", "# ------------------------------\n", "# 4. <PERSON>\n", "# ------------------------------\n", "rf = RandomForestClassifier(\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42\n", "    # class_weight supprimé pour ne pas équilibrer\n", ")\n", "rf.fit(X_train, y_train)  # sans sample_weight\n", "y_pred_rf = rf.predict(X_test)\n", "results.append({\n", "    'Model': 'Random Forest',\n", "    'F1_macro': f1_score(y_test, y_pred_rf, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_rf)\n", "})\n", "print(\"📕 Random Forest Classification Report:\")\n", "print(classification_report(y_test, y_pred_rf))\n", "\n", "# ------------------------------\n", "# R<PERSON><PERSON><PERSON> des performances\n", "# ------------------------------\n", "df_results = pd.DataFrame(results)\n", "print(\"\\n📊 Comparaison des modèles :\")\n", "print(df_results.sort_values(by='F1_macro', ascending=False))\n"]}, {"cell_type": "code", "execution_count": 12, "id": "8405f72d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [16:20:58] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [16:21:17] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [16:21:35] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [16:21:53] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [16:22:10] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy moyenne : 0.6577\n", "F1-score macro moyen : 0.6324\n"]}], "source": ["from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import accuracy_score, f1_score\n", "import numpy as np\n", "import xgboost as xgb\n", "\n", "\n", "\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "accuracies = []\n", "f1_scores = []\n", "models = []\n", "\n", "for train_idx, test_idx in cv.split(X, y):\n", "    X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]\n", "    y_train, y_test = y[train_idx], y[test_idx]\n", "    \n", "    model = xgb.XGBClassifier(\n", "        objective='multi:softmax',\n", "        num_class=len(np.unique(y)),\n", "        eval_metric='mlogloss',\n", "        use_label_encoder=False,\n", "        random_state=42\n", "    )\n", "    \n", "    # Entraînement\n", "    model.fit(X_train, y_train)\n", "    models.append(model)  # garder le modèle si besoin\n", "    \n", "    # Prédictions\n", "    y_pred = model.predict(X_test)\n", "    \n", "    # Évaluation\n", "    accuracies.append(accuracy_score(y_test, y_pred))\n", "    f1_scores.append(f1_score(y_test, y_pred, average='macro'))\n", "\n", "print(f\"Accuracy moyenne : {np.mean(accuracies):.4f}\")\n", "print(f\"F1-score macro moyen : {np.mean(f1_scores):.4f}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "1af87529", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[learning_curve] Training set sizes: [ 75216 150433 225650 300867 376084 451300 526517 601734 676951 752168]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  50 out of  50 | elapsed:  6.6min finished\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import learning_curve, StratifiedKFold\n", "from sklearn.metrics import make_scorer, f1_score\n", "\n", "\n", "scorer = make_scorer(f1_score, average='macro')  # 🔧 <PERSON><PERSON><PERSON>i ici\n", "\n", "train_sizes, train_scores, val_scores = learning_curve(\n", "    estimator=model,\n", "    X=X_train,\n", "    y=y_train,\n", "    cv=cv,\n", "    scoring=scorer,  # ✅ Pa<PERSON> d'erreur ici maintenant\n", "    train_sizes=np.linspace(0.1, 1.0, 10),\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "\n", "# Calcul des moyennes et écarts-types\n", "train_scores_mean = np.mean(train_scores, axis=1)\n", "val_scores_mean = np.mean(val_scores, axis=1)\n", "\n", "# Tracer la courbe\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(train_sizes, train_scores_mean, 'o-', label='Train f1-score', color='blue')\n", "plt.plot(train_sizes, val_scores_mean, 'o-', label='Validation f1-score', color='orange')\n", "plt.title('Courbe d’apprentissage - XGBoost (f1_macro)')\n", "plt.xlabel('<PERSON><PERSON> de l\\'échantillon d\\'entraînement')\n", "plt.ylabel('Score f1 macro')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "ea559329", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000357 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 259\n", "[LightGBM] [Info] Number of data points in the train set: 75216, number of used features: 3\n", "[LightGBM] [Info] Start training from score -0.856192\n", "[LightGBM] [Info] Start training from score -2.731129\n", "[LightGBM] [Info] Start training from score -1.224967\n", "[LightGBM] [Info] Start training from score -2.080506\n", "[LightGBM] [Info] Start training from score -2.392036\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[76]\tvalid_0's multi_logloss: 0.797725\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000730 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 150433, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.857154\n", "[LightGBM] [Info] Start training from score -2.724626\n", "[LightGBM] [Info] Start training from score -1.231239\n", "[LightGBM] [Info] Start training from score -2.076952\n", "[LightGBM] [Info] Start training from score -2.377107\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[86]\tvalid_0's multi_logloss: 0.79593\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000920 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 225650, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.860142\n", "[LightGBM] [Info] Start training from score -2.716081\n", "[LightGBM] [Info] Start training from score -1.228497\n", "[LightGBM] [Info] Start training from score -2.078883\n", "[LightGBM] [Info] Start training from score -2.375559\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[88]\tvalid_0's multi_logloss: 0.79535\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001495 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 300867, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.859839\n", "[LightGBM] [Info] Start training from score -2.717810\n", "[LightGBM] [Info] Start training from score -1.229491\n", "[LightGBM] [Info] Start training from score -2.075074\n", "[LightGBM] [Info] Start training from score -2.377719\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[96]\tvalid_0's multi_logloss: 0.79498\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.002224 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 376084, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.857944\n", "[LightGBM] [Info] Start training from score -2.721634\n", "[LightGBM] [Info] Start training from score -1.230742\n", "[LightGBM] [Info] Start training from score -2.076245\n", "[LightGBM] [Info] Start training from score -2.378128\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[108]\tvalid_0's multi_logloss: 0.794908\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.003008 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 451300, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.858781\n", "[LightGBM] [Info] Start training from score -2.719034\n", "[LightGBM] [Info] Start training from score -1.229671\n", "[LightGBM] [Info] Start training from score -2.077856\n", "[LightGBM] [Info] Start training from score -2.377348\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[107]\tvalid_0's multi_logloss: 0.794617\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.003848 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 526517, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.859489\n", "[LightGBM] [Info] Start training from score -2.717672\n", "[LightGBM] [Info] Start training from score -1.228864\n", "[LightGBM] [Info] Start training from score -2.076114\n", "[LightGBM] [Info] Start training from score -2.379989\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[112]\tvalid_0's multi_logloss: 0.793989\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.003609 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 601734, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.859407\n", "[LightGBM] [Info] Start training from score -2.718867\n", "[LightGBM] [Info] Start training from score -1.228854\n", "[LightGBM] [Info] Start training from score -2.074941\n", "[LightGBM] [Info] Start training from score -2.381129\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[115]\tvalid_0's multi_logloss: 0.794001\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.002972 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 676951, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.859435\n", "[LightGBM] [Info] Start training from score -2.717491\n", "[LightGBM] [Info] Start training from score -1.228771\n", "[LightGBM] [Info] Start training from score -2.075736\n", "[LightGBM] [Info] Start training from score -2.381169\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[133]\tvalid_0's multi_logloss: 0.793927\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.004561 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 752168, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.859881\n", "[LightGBM] [Info] Start training from score -2.717498\n", "[LightGBM] [Info] Start training from score -1.227956\n", "[LightGBM] [Info] Start training from score -2.075578\n", "[LightGBM] [Info] Start training from score -2.381921\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[129]\tvalid_0's multi_logloss: 0.794014\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAm0AAAHHCAYAAADtZG+rAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuNSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/xnp5ZAAAACXBIWXMAAA9hAAAPYQGoP6dpAADOGUlEQVR4nOzdd3xN5x/A8c/N3ktEgkgiMWLEiBV7NvaoluJXexS1q6jaLa1RtFp0oUVLKTUbau+9g0hIbEFISGTd+/z+OHK5kpCQOBnP+/W6r5x77hnfe3LH9z5TI4QQSJIkSZIkSTmakdoBSJIkSZIkSa8mkzZJkiRJkqRcQCZtkiRJkiRJuYBM2iRJkiRJknIBmbRJkiRJkiTlAjJpkyRJkiRJygVk0iZJkiRJkpQLyKRNkiRJkiQpF5BJmyRJkiRJUi4gk7YsMnHiRDQaDffu3VP1/NLLLV68GI1GQ3h4uNqhSK9h586daDQadu7c+dr7rlq1KusDk7KVRqNh4sSJaoehOk9PT7p3767a+a9du4aFhQX79u3LluOn9flcv3596tevr7+v1vs4q77jk5KScHd354cffnit/XNl0hYWFka/fv0oXrw4FhYW2NnZUatWLebOncuTJ0/UDi/H8PT0zLcfdFOnTmXt2rVqhyFlgto/fJ63fPly5syZk+7jMTExfPnll1SpUgV7e3vMzc3x8PCgY8eObNy40WDblC+Z529OTk7UqFGDZcuWpTq2p6cnGo2Gxo0bp3nun376SX+co0ePvtHzTBEXF8fEiRNfKxnOizZt2pRvPztfZvLkyVSvXp1atWqpHUquZWpqyvDhw/nyyy+Jj4/P9P4m2RBTttq4cSPvv/8+5ubmdO3alXLlypGYmMjevXsZOXIk586d48cff1Q7TEllU6dO5b333qNt27YG6z/88EM++OADzM3N1QlMeiN169blyZMnmJmZZet5li9fztmzZxk6dGiqx0JDQwkMDCQiIoJ27drRtWtXbGxsuHbtGps2baJly5b89ttvfPjhhwb7DR48mKpVqwJw//59VqxYwf/+9z8ePnzIwIEDDba1sLBgx44d3L59G1dXV4PHli1bhoWFxWt94KcnLi6OSZMmARiUauRXmzZt4vvvv89xidvFixcxMlKnrOXu3bssWbKEJUuWqHL+vKRHjx6MHj2a5cuX07Nnz0ztm6uStitXrvDBBx/g4eHB9u3bcXNz0z82cOBAQkNDU/3KzW6xsbFYW1u/1XPmFfHx8ZiZmb3VDyFjY2OMjY3f2vmkrGVkZISFhYVq509OTqZdu3bcuXOHXbt2pSpxmDBhAlu2bEGr1abat06dOrz33nv6+/3796d48eIsX748VdJWq1Ytjhw5wooVKxgyZIh+/fXr19mzZw/t2rVj9erVWfzschadTkdiYqKq/++cRs0fm0uXLsXExIRWrVqpFkNe4eDgwDvvvMPixYsznbTlqurR6dOn8/jxY3755ReDhC2Fj4+PwQdccnIyU6ZMwdvbG3Nzczw9Pfnss89ISEgw2C+99hIvth9IqW/ftWsXAwYMwMXFhaJFixrsc+/ePTp06ICdnR0FChRgyJAhaf4iXrp0Kf7+/lhaWuLk5MQHH3zAtWvXMnQd9u7dS9WqVbGwsMDb25uFCxdmaL+oqCg++eQTypcvj42NDXZ2djRr1oxTp04ZbJdSnbNixQo+++wzXF1dsba2pnXr1qlirF+/PuXKlePYsWPUrFkTS0tLvLy8WLBgQZrH/PPPP/n8888pUqQIVlZWxMTEAHDo0CGaNm2Kvb09VlZW1KtXL1W7iZTqs9DQULp3746DgwP29vb06NGDuLg4/XYajYbY2FiWLFmir0ZK+T+m1Wbi6NGjBAYG4uzsrI//xTfSn3/+ib+/P7a2ttjZ2VG+fHnmzp2b6WsLEBERQevWrbG2tsbFxYVhw4YRFBSUZlutjFyXtCQmJjJ+/Hj8/f2xt7fH2tqaOnXqsGPHDv02SUlJODk50aNHj1T7x8TEYGFhwSeffKJfl5CQwIQJE/Dx8cHc3Bx3d3c+/fTTVO8nUF7f1apVw8rKCkdHR+rWrcuWLVteGferpNem7fvvv6d48eJYWlpSrVo19uzZk6otTAqdTseXX35J0aJFsbCwoFGjRoSGhuofr1+/Phs3biQiIkL/+vH09ATgr7/+4uzZs4wbNy7dKqJ33nmHZs2avfK5mJmZ4ejoiIlJ6t/OFhYWvPvuuyxfvtxg/R9//IGjoyOBgYGvPH6Khw8fMnToUNzd3TE3N8fHx4evv/4anU4HQHh4OAULFgRg0qRJ+uf8qlKmVx03xcyZM6lZsyYFChTA0tISf3//NNsjaTQaPv74Y5YtW0bZsmUxNzfn33//TbXdjh070Gg0rFmzJtVjy5cvR6PRcODAgXTjTkpKYtKkSZQoUQILCwsKFChA7dq12bp1KwDdu3fn+++/18eUcksRGxvLiBEj9M+7VKlSzJw5EyFEus+nVKlSWFhY4O/vz+7duw22S/lcu3Dhwiu/O9L7Ttq3bx/Dhw+nYMGCWFtb065dO+7evWuwr06nY+LEiRQuXBgrKysaNGhAcHBwhtvJrV27lurVq2NjY5PqsUOHDtG8eXMcHR2xtrbGz8/P4PPx9OnTdO/eXd+kydXVlZ49e3L//v1Xnjc9Wq32ld9Pe/bs4f3336dYsWL6z6xhw4al2Ywq5foXLFgQS0tLSpUqxdixY18aQ0REBD4+PpQrV447d+4AcOnSJdq3b4+rqysWFhYULVqUDz74gOjoaIN9mzRpwt69e4mKisrU885VJW3r16+nePHi1KxZM0Pb9+7dmyVLlvDee+8xYsQIDh06xLRp0zh//nyab/iMGjBgAAULFmT8+PHExsYaPNahQwc8PT2ZNm0aBw8e5Ntvv+XBgwf89ttv+m2+/PJLxo0bR4cOHejduzd3797lu+++o27dupw4cQIHB4d0z33mzBneeecdChYsyMSJE0lOTmbChAkUKlTolXFfvnyZtWvX8v777+Pl5cWdO3dYuHAh9erVIzg4mMKFCxts/+WXX6LRaBg1ahSRkZHMmTOHxo0bc/LkSSwtLfXbPXjwgObNm9OhQwc6derEypUr6d+/P2ZmZqmSnylTpmBmZsYnn3xCQkICZmZmbN++nWbNmuHv78+ECRMwMjJi0aJFNGzYkD179lCtWrVU19jLy4tp06Zx/Phxfv75Z1xcXPj6668B+P333+nduzfVqlWjb9++AHh7e6d5TSIjI/XXc/To0Tg4OBAeHs7ff/+t32br1q106tSJRo0a6c9x/vx59u3bp/+RkNFrGxsbS8OGDbl16xZDhgzB1dWV5cuXGyRTKTJ7XZ4XExPDzz//TKdOnejTpw+PHj3il19+ITAwkMOHD1OxYkVMTU1p164df//9NwsXLjSocly7di0JCQl88MEHgPKB37p1a/bu3Uvfvn3x9fXlzJkzzJ49m5CQEIP2g5MmTWLixInUrFmTyZMnY2ZmxqFDh9i+fTvvvPNOujG/rvnz5/Pxxx9Tp04dhg0bRnh4OG3btsXR0THVjyqAr776CiMjIz755BOio6OZPn06Xbp04dChQwCMHTuW6Ohorl+/zuzZswH0X1Tr168H4H//+1+m43z06JG+vV5UVJS+CvaXX35Jc/vOnTvzzjvvEBYWpn/9Ll++nPfeew9TU9MMnTMuLo569epx48YN+vXrR7Fixdi/fz9jxozh1q1bzJkzh4IFCzJ//nz69+9Pu3btePfddwHw8/N7o+OmmDt3Lq1bt6ZLly4kJiby559/8v7777NhwwZatGhhcNzt27ezcuVKPv74Y5ydnfXJ8vPq16+Pu7s7y5Yto127dgaPLVu2DG9vbwICAtKNfeLEiUybNk3/GRETE8PRo0c5fvw4TZo0oV+/fty8eZOtW7fy+++/G+wrhKB169bs2LGDXr16UbFiRYKCghg5ciQ3btzQv15S7Nq1ixUrVjB48GDMzc354YcfaNq0KYcPH6ZcuXIG22bkuyM9gwYNwtHRkQkTJhAeHs6cOXP4+OOPWbFihX6bMWPGMH36dFq1akVgYCCnTp0iMDAwQ9XsSUlJHDlyhP79+6d6bOvWrbRs2RI3Nzf9Z9r58+fZsGGD/vNx69atXL58mR49euDq6qpvxnTu3DkOHjz4Wp3oMvL99NdffxEXF0f//v0pUKAAhw8f5rvvvuP69ev89ddf+mOdPn2aOnXqYGpqSt++ffH09CQsLIz169fz5Zdfpnn+sLAwGjZsiJOTE1u3bsXZ2ZnExEQCAwNJSEhg0KBBuLq6cuPGDTZs2MDDhw+xt7fX7+/v748Qgv3799OyZcuMP3GRS0RHRwtAtGnTJkPbnzx5UgCid+/eBus/+eQTAYjt27fr1wFiwoQJqY7h4eEhunXrpr+/aNEiAYjatWuL5ORkg20nTJggANG6dWuD9QMGDBCAOHXqlBBCiPDwcGFsbCy+/PJLg+3OnDkjTExMUq1/Udu2bYWFhYWIiIjQrwsODhbGxsbiVf/O+Ph4odVqDdZduXJFmJubi8mTJ+vX7dixQwCiSJEiIiYmRr9+5cqVAhBz587Vr6tXr54AxKxZs/TrEhISRMWKFYWLi4tITEw0OGbx4sVFXFycfludTidKlCghAgMDhU6n06+Pi4sTXl5eokmTJvp1Kde4Z8+eBs+hXbt2okCBAgbrrK2tDf53KVL+h1euXBFCCLFmzRoBiCNHjqR73YYMGSLs7OxS/c+fl9FrO2vWLAGItWvX6tc9efJElC5dWgBix44dQojMXZe0JCcni4SEBIN1Dx48EIUKFTK4fkFBQQIQ69evN9i2efPmonjx4vr7v//+uzAyMhJ79uwx2G7BggUCEPv27RNCCHHp0iVhZGQk2rVrl+p6PP880pLy/717926626S8jlKuU0JCgihQoICoWrWqSEpK0m+3ePFiAYh69eql2tfX19fg2sydO1cA4syZM/p1LVq0EB4eHqnOX6lSJeHg4JBq/ePHj8Xdu3f1t+jo6FTnffFmZGSU5vvdw8NDtGjRQiQnJwtXV1cxZcoUIYTyPgfErl279K/jl71uhRBiypQpwtraWoSEhBisHz16tDA2NhZXr14VQghx9+7ddD8H3+S4QgiD97sQQiQmJopy5cqJhg0bGqxPuSbnzp1Ldb4XYxszZowwNzcXDx8+1K+LjIwUJiYmr3wOFSpUEC1atHjpNgMHDkzz83Tt2rUCEF988YXB+vfee09oNBoRGhpqEDMgjh49ql8XEREhLCwsRLt27fTrMvrdIUT630mNGzc2eH8NGzZMGBsb66/P7du3hYmJiWjbtq3BOSZOnCiAND8rnxcaGioA8d133xmsT05OFl5eXsLDw0M8ePDA4LEXP7de9McffwhA7N69O9XzSfl8FkL5jknrfZyR76e0zjtt2jSh0WgMvkPr1q0rbG1tDda9+Bye/3w6f/68KFy4sKhataqIiorSb3PixAkBiL/++ivVeV908+ZNAYivv/76lds+L9dUj6ZUo9na2mZo+02bNgEwfPhwg/UjRowAeKO2b3369Em3XdSLbVMGDRpkEM/ff/+NTqejQ4cO3Lt3T39zdXWlRIkSaZa4pNBqtQQFBdG2bVuKFSumX+/r65uh6hJzc3N9+zGtVsv9+/exsbGhVKlSHD9+PNX2Xbt2Nbje7733Hm5ubvrnksLExIR+/frp75uZmdGvXz8iIyM5duyYwbbdunUzKKU7efIkly5donPnzty/f19/PWJjY2nUqBG7d+9OVd3y0UcfGdyvU6cO9+/f179GMiOlVHPDhg0kJSWlu01sbKy++iQtGb22//77L0WKFKF169b6dRYWFvTp08fgeK9zXZ5nbGysLznT6XRERUWRnJxMlSpVDOJp2LAhzs7OBr/IHzx4wNatW+nYsaN+3V9//YWvry+lS5c2eN02bNgQQP+6Xbt2LTqdjvHjx6dqq5gdQ9IcPXqU+/fv06dPH4Nqxi5duuDo6JjmPj169DAoVaxTpw6glJa+SkxMTJrVQ2PHjqVgwYL6W+fOnVNtM378eLZu3crWrVtZsWIFnTp1YuzYsQbVSM8zNjamQ4cO/PHHH4BSiuTu7q6PNyP++usv6tSpg6Ojo8H/rXHjxmi12lRVddlx3BdL5aOjo6lTp06anzn16tWjTJkyrzx/165dSUhIMKhmXbFiBcnJya8sBXVwcODcuXNcunQpI0/VwKZNmzA2Nmbw4MEG60eMGIEQgs2bNxusDwgIwN/fX3+/WLFitGnThqCgoFTtHl/13fEyffv2NXh/1alTB61WS0REBADbtm0jOTmZAQMGpHmOV0mpxnzxPXXixAmuXLnC0KFDU9UQPR/P86+B+Ph47t27R40aNQDSfB1kREa+n54/b2xsLPfu3aNmzZoIIThx4gSgdLDYvXs3PXv2NPheffE5pDh79iz16tXD09OT//77z+CapJSkBQUFGTTZSUvKfpntLZ9rqkft7OwApYohIyIiIjAyMsLHx8dgvaurKw4ODvoX8+vw8vJK97ESJUoY3Pf29sbIyEjfhurSpUsIIVJtl+Jl1R53797lyZMnae5bqlSpV765dTodc+fO5YcffuDKlSsGHxoFChR45XPRaDT4+PikGuOscOHCqTpjlCxZElDay6S8OSH1tUv54OzWrVu6cUdHRxu8MV58Y6U89uDBA/3rJKPq1atH+/btmTRpErNnz6Z+/fq0bduWzp076xv9DhgwgJUrV9KsWTOKFCnCO++8Q4cOHWjatKn+OBm9thEREXh7e6f6MHjxdfo61+VFS5YsYdasWVy4cMEgIX3+f2BiYkL79u1Zvnw5CQkJmJub8/fff5OUlGSQtF26dInz58/r2z69KDIyElCqDIyMjDL0xZsVUt7HL14/ExOTNKvW4OWvn1extbVNsx3OgAED9FUc6SUN5cuXNxjGo0OHDkRHRzN69Gg6d+6c5rXt3Lkz3377LadOnWL58uV88MEHmUp+L126xOnTp1/5f8uszBx3w4YNfPHFF5w8edKg/WNaz+Nln63PK126NFWrVmXZsmX06tULUJLaGjVqpHotvGjy5Mm0adOGkiVLUq5cOZo2bcqHH3740urgFBERERQuXDhV4YGvr6/+8eel9VldsmRJ4uLiuHv3rkHP4Fd9d7zMq17T6b1PnJycXvoZ8iLxQru9sLAwgFRVvS+Kiopi0qRJ/Pnnn6lecy+29cqojHw/Xb16lfHjx7Nu3bpU7++U86b8WHvVc0jRqlUrChUqRFBQUKofcF5eXgwfPpxvvvmGZcuWUadOHVq3bs3//vc/g6pReHYtM/tjNlclbYULF+bs2bOZ2u9Nft2n1QMMDLP3zJ5fp9Oh0WjYvHlzmqV1af2KzypTp05l3Lhx9OzZkylTpuDk5ISRkRFDhw59aalNVnrx2qWcd8aMGVSsWDHNfV68JumVcr74gZIRKYM0Hjx4kPXr1xMUFETPnj2ZNWsWBw8exMbGBhcXF06ePElQUBCbN29m8+bNLFq0iK5du+q7v2f1tX2d6/K8pUuX0r17d9q2bcvIkSNxcXHB2NiYadOm6T9oU3zwwQcsXLiQzZs307ZtW1auXEnp0qWpUKGCQTzly5fnm2++SfN87u7umXyG6nmT10/p0qU5efIkN27coEiRIvr1JUuW1P9QyUxvx0aNGrFhwwYOHz6cqn0XQPXq1fH29mbo0KFcuXIlzRK8l9HpdDRp0oRPP/00zcdTYs6sjB53z549tG7dmrp16/LDDz/g5uaGqakpixYtStXJAjL32dq1a1eGDBnC9evXSUhI4ODBg8ybN++V+9WtW5ewsDD++ecftmzZws8//8zs2bNZsGABvXv3zvD5s1tmvruy8jMxLSk/PDPywyYtHTp0YP/+/YwcOZKKFStiY2ODTqejadOm2fbdo9VqadKkCVFRUYwaNYrSpUtjbW3NjRs36N69+2uft3379ixZsoRly5YZ1DClmDVrFt27d9e/vgYPHqxvp/h8G9uUa+ns7Jyp8+eapA2gZcuW/Pjjjxw4cOClDU0BPDw80Ol0XLp0Sf8rCODOnTs8fPgQDw8P/TpHR0cePnxosH9iYiK3bt3KdIyXLl0y+LUYGhqKTqfT/+r39vZGCIGXl1emPzBTerWkVax/8eLFV+6/atUqGjRokKrh88OHD9N84bx4HiEEoaGhqX6R3rx5M9XQJyEhIQDplnakSGlgbWdnl+5goq8js8l6jRo1qFGjBl9++SXLly+nS5cu/Pnnn/oPcTMzM1q1akWrVq3Q6XQMGDCAhQsXMm7cOHx8fDJ8bT08PAgODkYIYRDj870X4c2vy6pVqyhevDh///23wXkmTJiQatu6devi5ubGihUrqF27Ntu3b0/Va8rb25tTp07RqFGjl15bb29vdDodwcHB6SabWSnlfRwaGkqDBg3065OTkwkPD89Q6Ula0nuOLVu25M8//2TZsmXpJiyZkZycDMDjx4/T3aZTp0588cUX+Pr6Zvqaent78/jx41e+hjL7fsnocVevXo2FhQVBQUEGw1UsWrQoU+dLywcffMDw4cP5448/ePLkCaampgalwy+T0mu6R48ePH78mLp16zJx4kT9+z296+Hh4cF///3Ho0ePDErbLly4oH/8eWl9VoeEhGBlZZWqlPJV3x1v4vn3yfPnuH//foYSsWLFimFpacmVK1cM1qd8Tp09ezbd18KDBw/Ytm0bkyZNYvz48fr1r1M9/bxXfT+dOXOGkJAQlixZQteuXfXbvdjMpXjx4vrnkBEzZszAxMSEAQMGYGtrm+YPqfLly1O+fHk+//xz9u/fT61atViwYAFffPGFfpuUa/l8fpIRuaZNG8Cnn36KtbU1vXv31nevfV5YWJi+fUjz5s0BUo1qnlJS8PyvWm9v71RtO3788cd0S9peJqWreIrvvvsOQD8EwLvvvouxsTGTJk1K9StICPHSLtDGxsYEBgaydu1arl69ql9//vx5goKCXhmbsbFxqnP+9ddf3LhxI83tf/vtN4Pq6FWrVnHr1q1UwxkkJycbDDuSmJjIwoULKViwoEF7jrT4+/vj7e3NzJkz0/zierHbekZZW1unSsTT8uDBg1TXJOWLMaUq58X/iZGRkf6DIWWbjF7bwMBAbty4wbp16/Tr4uPj+emnnwy2e9PrkvLL+/mYDh06lOZQCEZGRrz33nusX7+e33//neTk5FRffh06dODGjRup4gR48uSJvhd127ZtMTIyYvLkyal+yWbVr/7nValShQIFCvDTTz/pEyBQqspet1QAlNdPWtU2HTp0oEyZMkyZMoWDBw+muW9mnueGDRsADEo1X9S7d28mTJjArFmzMnzcFB06dODAgQNpfj48fPhQf82srKz067LyuMbGxmg0GoPP0vDw8CyZrcTZ2ZlmzZqxdOlSli1bRtOmTTNUavHi+9nGxgYfHx+DqtuUH6AvXo/mzZuj1WpTlejNnj0bjUaT6rPxwIEDBm22rl27xj///MM777yTqnTsVd8db6JRo0aYmJgwf/58g/UZKZkEpdlOlSpVUs3AUblyZby8vJgzZ06qa5XyPkjrswhSfzdn1qu+n9I6rxAiVRvSggULUrduXX799VeD79W0YgYlof/xxx9577336Natm8FneUxMjMHnECgJnJGRUaqhkY4dO4ZGo3llAdSLclVJm7e3N8uXL6djx474+voazIiwf/9+/vrrL/14MxUqVKBbt278+OOPPHz4kHr16nH48GGWLFlC27ZtDX6V9+7dm48++oj27dvTpEkTTp06RVBQUKaLLUHJnlu3bk3Tpk05cOAAS5cupXPnzvoPZW9vb7744gvGjBmjH5rA1taWK1eusGbNGvr27WswNtaLJk2axL///kudOnUYMGAAycnJfPfdd5QtW5bTp0+/NLaWLVsyefJkevToQc2aNTlz5gzLli3T/9J4kZOTE7Vr16ZHjx7cuXOHOXPm4OPjk6rRfOHChfn6668JDw+nZMmSrFixgpMnT/Ljjz++cmgCIyMjfv75Z5o1a0bZsmXp0aMHRYoU4caNG+zYsQM7Ozv9MAuZ4e/vz3///cc333xD4cKF8fLyonr16qm2W7JkCT/88APt2rXD29ubR48e8dNPP2FnZ6dP/Hv37k1UVBQNGzakaNGiRERE8N1331GxYkX9r6SMXtt+/foxb948OnXqxJAhQ3Bzc9OPcA/PfuG/6XVp2bIlf//9N+3ataNFixZcuXKFBQsWUKZMmTSTwI4dO/Ldd98xYcIEypcvn+rX34cffsjKlSv56KOP2LFjB7Vq1UKr1XLhwgVWrlxJUFAQVapUwcfHh7FjxzJlyhTq1KnDu+++i7m5OUeOHKFw4cJMmzbtlf+7b775Rp9EpDAyMuKzzz5Lta2ZmRkTJ05k0KBBNGzYkA4dOhAeHs7ixYvTbDuYUf7+/qxYsYLhw4dTtWpVbGxsaNWqFaampqxZs4bAwEBq167Nu+++S506dfTVLuvWrePq1atpVnXu2bNHP7xCVFQU69atY9euXXzwwQeULl063Vg8PDxee2T+kSNHsm7dOlq2bEn37t3x9/cnNjaWM2fOsGrVKsLDw/XjE5YpU4YVK1ZQsmRJnJycKFeuXLrtfDJ63BYtWvDNN9/QtGlTOnfuTGRkJN9//z0+Pj6v/LzKiK5du+oHLJ4yZUqG9ilTpgz169fH398fJycnjh49yqpVq/j444/126T82Bw8eDCBgYEYGxvzwQcf0KpVKxo0aMDYsWMJDw+nQoUKbNmyhX/++YehQ4emGlqoXLlyBAYGGgz5Aehnn3jeq7473kShQoUYMmQIs2bN0p/j1KlTbN68GWdn5wy9T9q0acPYsWOJiYnRtx02MjJi/vz5tGrViooVK9KjRw/c3Ny4cOEC586dIygoCDs7O+rWrcv06dNJSkqiSJEibNmyJVWpXWa96vupdOnSeHt788knn3Djxg3s7OxYvXp1mj/mvv32W2rXrk3lypXp27cvXl5ehIeHs3HjRk6ePJlqeyMjI5YuXUrbtm3p0KEDmzZtomHDhmzfvp2PP/6Y999/n5IlS5KcnMzvv/+OsbEx7du3NzjG1q1bqVWrVprtyV8qU31Nc4iQkBDRp08f4enpKczMzIStra2oVauW+O6770R8fLx+u6SkJDFp0iTh5eUlTE1Nhbu7uxgzZozBNkIIodVqxahRo4Szs7OwsrISgYGBIjQ0NN3u1Wl1s0/pDhwcHCzee+89YWtrKxwdHcXHH38snjx5kmr71atXi9q1awtra2thbW0tSpcuLQYOHCguXrz4yue/a9cu4e/vL8zMzETx4sXFggUL9Od/mfj4eDFixAjh5uYmLC0tRa1atcSBAwfS7VL9xx9/iDFjxggXFxdhaWkpWrRokapLdL169UTZsmXF0aNHRUBAgLCwsBAeHh5i3rx5BtulHDO9rtAnTpwQ7777rihQoIAwNzcXHh4eokOHDmLbtm36bdIbEiKtbuIXLlwQdevWFZaWlgZd2l/c9vjx46JTp06iWLFiwtzcXLi4uIiWLVsadNNftWqVeOedd4SLi4swMzMTxYoVE/369RO3bt3K9LUVQojLly+LFi1aCEtLS1GwYEExYsQIsXr1agGIgwcPZvq6pEWn04mpU6cKDw8PYW5uLipVqiQ2bNggunXrluZQFjqdTri7u6c5nEGKxMRE8fXXX4uyZcsKc3Nz4ejoKPz9/cWkSZMMhrgQQohff/1VVKpUSb9dvXr1xNatW18ac8r/N62bsbGxECL1kB8pvv32W/1zrVatmti3b5/w9/cXTZs21W+T3mvwypUrAhCLFi3Sr3v8+LHo3LmzcHBwEECqa/bw4UMxefJkUalSJWFjYyPMzMyEu7u7eO+991INn5LWkB9mZmaidOnS4ssvv9QPi5MiZciPl8nokB9CCPHo0SMxZswY4ePjI8zMzISzs7OoWbOmmDlzpsG59+/fr/9cIQPDf2T0uL/88osoUaKEMDc3F6VLlxaLFi1K8/MKEAMHDkzzXOnFk5CQIBwdHYW9vX2an7Np+eKLL0S1atWEg4ODsLS0TPP/kJycLAYNGiQKFiwoNBqNQayPHj0Sw4YNE4ULFxampqaiRIkSYsaMGamGtEl5PkuXLtU//0qVKqV67WbmuyOj30lpvU+Sk5PFuHHjhKurq7C0tBQNGzYU58+fFwUKFBAfffTRK6/bnTt3hImJifj9999TPbZ3717RpEkTYWtrK6ytrYWfn5/B8CDXr18X7dq1Ew4ODsLe3l68//77+iEvnv+/ZmbIj4x8PwUHB4vGjRsLGxsb4ezsLPr06SNOnTqV6v0uhBBnz57Vx2hhYSFKlSolxo0bp388re+fuLg4Ua9ePWFjYyMOHjwoLl++LHr27Cm8vb2FhYWFcHJyEg0aNBD//fefwbkePnwozMzMxM8///zK6/6iXJm0SdnrVQnW81KSNunNzJ49WwDi+vXraoeSJ2i1WuHk5JRqnEYpb0lKShIFCxZMNXZjTvCyJPR5GRmfMLs8ePDgpT/UXtSzZ09Ru3btbI4q75s9e7Zwc3NLcxy5V8lVbdokKS94cQqV+Ph4Fi5cSIkSJQx6JEoZEx8fn6rtyW+//UZUVJSc/DyPW7t2LXfv3jVoaC6lLa2pm1LalWX0fTJhwgSOHDmSoan0pLQlJSXxzTff8Pnnn2eqt3SKXNWmTZLygnfffZdixYpRsWJFoqOjWbp0KRcuXGDZsmVqh5YrHTx4kGHDhvH+++9ToEABjh8/zi+//EK5cuV4//331Q5PygaHDh3i9OnTTJkyhUqVKlGvXj21Q8rxVqxYweLFi2nevDk2Njbs3buXP/74g3feeSfdeXRfVKxYsQxNeyWlz9TUNFWHh8yQSZskvWWBgYH8/PPPLFu2DK1WS5kyZfjzzz8zPFyBZMjT0xN3d3e+/fZboqKicHJyomvXrnz11VcGMx9Iecf8+fNZunQpFStWZPHixWqHkyv4+flhYmLC9OnTiYmJ0XdOeH4YCinn04gX6xUkSZIkSZKkHEe2aZMkSZIkScoFZNImSZIkSZKUC8g2ba9Jp9Nx8+ZNbG1t32h+U0mSJEmS3h4hBI8ePaJw4cIYGeWusiuZtL2mmzdv5qpJsiVJkiRJeubatWsGk7jnBjJpe00pkwVfu3ZNP6WHJEmSJEk5W0xMDO7u7vrv8dxEJm2vKaVK1M7OTiZtkiRJkpTL5MamTbmrMleSJEmSJCmfkkmbJEmSJElSLiCTNkmSJEmSpFxAtmnLZlqtlqSkJLXDkCQpjzA1NcXY2FjtMCRJUoFM2rKJEILbt2/z8OFDtUORJCmPcXBwwNXVNVc2pJYk6fXJpC2bpCRsLi4uWFlZyQ9XSZLemBCCuLg4IiMjAXBzc1M5IkmS3iaZtGUDrVarT9gKFCigdjiSJOUhlpaWAERGRuLi4iKrSiUpH5EdEbJBShs2KysrlSORJCkvSvlske1lJSl/kUlbNpJVotKr/PXXX/z+++9qhyHlMvKzRZLyJ5m0SXnGxIkTqVixotphZNipU6cYN24cM2bMYO/evWqH89ZdvHgRV1dXHj16pHYoOca///5LxYoV0el0aociSVIOlCOStu+//x5PT08sLCyoXr06hw8ffun2Dx8+ZODAgbi5uWFubk7JkiXZtGmT/vH58+fj5+enn2IqICCAzZs3Gxyjfv36aDQag9tHH32ULc8vt7h79y79+/enWLFimJub4+rqSmBgIPv27VM7tNcyceJEunfvnuXHzIrEMDk5mf79+7N8+XL++usvBg8eTFxc3JsHqLL69eszdOjQDG07ZswYBg0apJ//b+fOnanekxqNhs8//xyA+Ph4unfvTvny5TExMaFt27bZ9CzU07RpU0xNTVm2bJnaoUiSlAOp3hFhxYoVDB8+nAULFlC9enXmzJlDYGAgFy9exMXFJdX2iYmJNGnSBBcXF1atWkWRIkWIiIjAwcFBv03RokX56quvKFGiBEIIlixZQps2bThx4gRly5bVb9enTx8mT56sv5/f26C1b9+exMRElixZQvHixblz5w7btm3j/v372XbOxMREzMzMsu34aklKSsLU1DTdx01MTNi/f7/+/vHjx99GWDnG1atX2bBhA999912qxy5evGgwn6+NjQ2gdPCxtLRk8ODBrF69+q3FmhW0Wi0ajQYjo1f/Tu7evTvffvstH3744VuITJKkXEWorFq1amLgwIH6+1qtVhQuXFhMmzYtze3nz58vihcvLhITEzN1HkdHR/Hzzz/r79erV08MGTLktWIWQojo6GgBiOjo6FSPPXnyRAQHB4snT5689vHftgcPHghA7Ny585Xb9e3bV7i4uAhzc3NRtmxZsX79ev3jq1atEmXKlBFmZmbCw8NDzJw502B/Dw8PMXnyZPHhhx8KW1tb0a1bNyGEEHv27BG1a9cWFhYWomjRomLQoEHi8ePHL41l2rRpwsXFRdjY2IiePXuKUaNGiQoVKugfnzBhgv74QiivralTpwpPT09hYWEh/Pz8xF9//aV/fMeOHQIQ//33n/D39xeWlpYiICBAXLhwQQghxKJFiwRgcFu0aJEQQghA/PDDD6JVq1bCyspKTJgwQSQnJ4uePXvqz1eyZEkxZ84cg+fQrVs30aZNG/39evXqiUGDBomRI0cKR0dHUahQITFhwoRU/4NevXoJZ2dnYWtrKxo0aCBOnjxp8LwrVKggfvnlF+Hu7i6sra1F//79RXJysvj6669FoUKFRMGCBcUXX3zxWsf97bffhIeHh7CzsxMdO3YUMTEx+ufy4vW5cuVKmv+7GTNmiCpVqhisS7n+Dx48SHOfl1239Fy5ckUAYsWKFfrXV5UqVcTFixfF4cOHhb+/v7C2thZNmzYVkZGR+v0OHz4sGjduLAoUKCDs7OxE3bp1xbFjx1Jdr/TeC4sWLRL29vbin3/+Eb6+vsLY2FhcuXJFREVFiQ8//FA4ODgIS0tL0bRpUxESEmJw3IiICAGI0NDQdJ9XbvyMyfGSk4RISlA7CukteNn3d06natKWkJAgjI2NxZo1awzWd+3aVbRu3TrNfZo1aya6dOki+vTpI1xcXETZsmXFl19+KZKTk9PcPjk5Wfzxxx/CzMxMnDt3Tr++Xr16wtnZWRQoUECULVtWjB49WsTGxqYba3x8vIiOjtbfrl27lqeStqSkJGFjYyOGDh0q4uPj09xGq9WKGjVqiLJly4otW7aIsLAwsX79erFp0yYhhBBHjx4VRkZGYvLkyeLixYti0aJFwtLSUp/YCCH0X/YzZ84UoaGh+pu1tbWYPXu2CAkJEfv27ROVKlUS3bt3TzfeFStWCHNzc/Hzzz+LCxcuiLFjxwpbW9uXJm1ffPGFKF26tPj3339FWFiYWLRokTA3N9cnqilJQ/Xq1cXOnTvFuXPnRJ06dUTNmjWFEELExcWJESNGiLJly4pbt26JW7duibi4OCGEkrS5uLiIX3/9VYSFhYmIiAiRmJgoxo8fL44cOSIuX74sli5dKqysrMSKFSv0MaWVtNnZ2YmJEyeKkJAQsWTJEqHRaMSWLVv02zRu3Fi0atVKHDlyRISEhIgRI0aIAgUKiPv37+uft42NjXjvvffEuXPnxLp164SZmZkIDAwUgwYNEhcuXBC//vqrAMTBgwczfdx3331XnDlzRuzevVu4urqKzz77TAghxMOHD0VAQIDo06eP/vqk975s3bq1+OijjwzWZWfSlvJ/Dw4OFjVq1BD+/v6ifv36Yu/eveL48ePCx8fHIJ5t27aJ33//XZw/f14EBweLXr16iUKFCukT1Fe9FxYtWiRMTU1FzZo1xb59+8SFCxdEbGysaN26tfD19RW7d+8WJ0+eFIGBgcLHxyfVj9BChQoZvG9elBs/Y3I0rVaIxS2FmFxQiH8/E+LxXbUjkrKRTNpe040bNwQg9u/fb7B+5MiRolq1amnuU6pUKWFubi569uwpjh49Kv7880/h5OQkJk6caLDd6dOnhbW1tTA2Nhb29vZi48aNBo8vXLhQ/Pvvv+L06dNi6dKlokiRIqJdu3bpxjphwoRUpQgZTdp0Op2ITUhS5abT6TL0vxBCKSVzdHQUFhYWombNmmLMmDHi1KlT+seDgoKEkZGRuHjxYpr7d+7cWTRp0sRg3ciRI0WZMmX09z08PETbtm0NtunVq5fo27evwbo9e/YIIyOjdL+UAgICxIABAwzWVa9e3SBpe158fLywsrJK9Vrr1auX6NSpkxDCsKQtxcaNGwWgjyOltOlFgBg6dGia537ewIEDRfv27fX300raateubbBP1apVxahRo4QQynWxs7NLlVh7e3uLhQsX6mO0srLSJxhCCBEYGCg8PT2FVqvVrytVqpS+RPt1jzty5EhRvXp1g/gzUoJdoUIFMXnyZIN1Kdff2tra4Hbv3r1U+2c2aXu+lP2PP/4QgNi2bZt+3bRp00SpUqXSPY5WqxW2trb6krRXvRdSSmWfL6kMCQkRgNi3b59+3b1794SlpaVYuXKlwf6VKlVK9Zn2PJm0ZbHTfwkxwe7Z7cvCQmz7QognD9WOTMoGuTlpU71NW2bpdDpcXFz48ccfMTY2xt/fnxs3bjBjxgwmTJig365UqVKcPHmS6OhoVq1aRbdu3di1axdlypQBoG/fvvpty5cvj5ubG40aNSIsLAxvb+9U5x0zZgzDhw/X34+JicHd3T1DMT9J0lJmfNDrPuU3Ejw5ECuzjP2b27dvT4sWLdizZw8HDx5k8+bNTJ8+nZ9//pnu3btz8uRJihYtSsmSJdPc//z587Rp08ZgXa1atZgzZw5arVY/CGiVKlUMtjl16hSnT582aHwthECn03HlyhV8fX3TPNeLHUcCAgLYsWNHmrGFhoYSFxdHkyZNDNYnJiZSqVIlg3V+fn765ZQR5yMjIylWrFiax07x4vMCpZPNr7/+ytWrV3ny5AmJiYmv7Mjw/PlTYkgZAf/UqVM8fvw41aDNT548ISwsTH/f09NT38AfoFChQhgbGxu0qSpUqNAbH/f52DLjyZMnWFhYpPnYnj17DM7h6OiY6eO/6PlrWqhQIUB53z+/7vnncefOHT7//HN27txJZGQkWq2WuLg4rl69CvDK9wKAmZmZwXnPnz+PiYkJ1atX168rUKAApUqV4vz58wb7Wlpa5omOKblCciJsn6Is+3WEuxfh1knYPR0O/wi1h0G1vmCWv9s8SzmDqkmbs7MzxsbG3Llzx2D9nTt3cHV1TXMfNze3VBMm+/r6cvv2bYNG7WZmZvj4+ADg7+/PkSNHmDt3LgsXLkzzuCkfpKGhoWkmbebm5pibm2f+SeYyFhYWNGnShCZNmjBu3Dh69+7NhAkT6N69u34k9jdlbW1tcP/x48f069ePwYMHp9r2VYlSRj1+/BiAjRs3UqRIEYPHXvy/Pt+BIGU8rIwMwfDi8/rzzz/55JNPmDVrFgEBAdja2jJjxgwOHTr00uO82IFBo9Hoz//48WPc3NzYuXNnqv2e74yT1jGy67ivMzyFs7MzDx48SPMxLy8vg3NmhbT+py+ue/55dOvWjfv37zN37lw8PDwwNzcnICCAxMREgAy9FywtLV97PLWoqCgKFiz4WvtKmXRsETwIB5tC0HI2mFrB+fWw/Qu4dxH+mwAHf4C6I6FyNzDJex2npNxD1aTNzMwMf39/tm3bpu++r9Pp2LZtGx9//HGa+9SqVYvly5ej0+n0pQYhISG4ubm9tBeiTqcjISEh3cdPnjwJZM9cfpamxgRPDszy42b03G+iTJkyrF27FlBKK65fv05ISEiaJQy+vr6phgfZt28fJUuWfOlUO5UrVyY4OFifZGeEr68vhw4domvXrvp1Bw8efOnzMDc35+rVq9SrVy/D53mRmZkZWq02Q9vu27ePmjVrMmDAAP2650utXkflypW5ffs2JiYmeHp6vtGxsuO4Gb0+lSpVIjg4+LXPk9327dvHDz/8QPPmzQG4du0a9+7d0z/+qvdCWnx9fUlOTubQoUPUrFkTgPv373Px4kV9DQAoQ5uEhYWlKgGWskF8DOz6WlmuPxrMnv7wKtMaSreA0yth5zR4GAGbPoH930K90UqJnHGuq6iS8gDVX3XDhw+nW7duVKlShWrVqjFnzhxiY2Pp0aMHAF27dqVIkSJMmzYNgP79+zNv3jyGDBnCoEGDuHTpElOnTjUopRkzZgzNmjWjWLFiPHr0iOXLl7Nz506CgpQqyrCwMJYvX07z5s0pUKAAp0+fZtiwYdStWzdV1VRW0Gg0Ga6iVMv9+/d5//336dmzJ35+ftja2nL06FGmT5+ur/KsV68edevWpX379nzzzTf4+Phw4cIFNBoNTZs2ZcSIEVStWpUpU6bQsWNHDhw4wLx58/jhhx9eeu5Ro0ZRo0YNPv74Y3r37o21tTXBwcFs3bqVefPmpbnPkCFD6N69O1WqVKFWrVosW7aMc+fOUbx48TS3t7W15ZNPPmHYsGHodDpq165NdHQ0+/btw87Ojm7dumXoOnl6enLlyhV99ZitrW26JbAlSpTgt99+IygoCC8vL37//XeOHDmCl5dXhs6VlsaNGxMQEEDbtm2ZPn06JUuW5ObNm2zcuJF27dqlWUX7No/r6enJoUOHCA8Px8bGBicnpzSHuQgMDKR3794G1eYZERwcTGJiIlFRUTx69Ej/YyurB1UuUaIEv//+O1WqVCEmJoaRI0calK696r2Q3jHbtGlDnz59WLhwIba2towePZoiRYoYNCs4ePCgvmRPymb7v4O4+1DAByq9MMSKkTFU7ATl2sOJ32DXDHh4Ff4ZAPvmQIOx4NsaMjCMiyRlFdVfbR07dmTmzJmMHz+eihUrcvLkSf799199u5OrV69y69Yt/fbu7u4EBQVx5MgR/Pz8GDx4MEOGDGH06NH6bSIjI+natSulSpWiUaNGHDlyhKCgIH17JjMzM/777z/eeecdSpcuzYgRI2jfvj3r169/u08+B7GxsaF69erMnj2bunXrUq5cOcaNG0efPn0MEqfVq1dTtWpVOnXqRJkyZfj000/1JSuVK1dm5cqV/Pnnn5QrV47x48czefLkVw5w6+fnx65duwgJCaFOnTpUqlSJ8ePHU7hw4XT36dixI+PGjePTTz/F39+fiIgI+vfv/9LzTJkyhXHjxjFt2jR8fX1p2rQpGzduzFQS1b59e5o2bUqDBg0oWLAgf/zxR7rb9uvXj3fffZeOHTtSvXp17t+/b1Dq9jo0Gg2bNm2ibt269OjRg5IlS/LBBx8QERGhf8+oedxPPvkEY2NjypQpQ8GCBfVtwF7UrFkzTExM+O+//zIVZ/PmzalUqRLr169n586dVKpUKVtKpH755RcePHhA5cqV+fDDDxk8eHCqcSNf9l5Iz6JFi/D396dly5YEBAQghGDTpk0GVbV//PEHXbp0yffjRma7R3fgwNPPtkYTwDidcRVNzKBqbxh8AppMAUtHuBcCf3WDH+vBpa0gxNuLW8rXNELIV9vriImJwd7enujoaIOBQEGp3rhy5QpeXl7pNraWpPzu+++/Z926dfoScAnu3btHqVKlOHr06Et/TMjPmCywYRgc/RWKVIHe/0FG2x/Gx8CB75Vb4tMp2IoFQMNx4Fkr++KVsszLvr9zOtVL2iRJyp/69etH3bp15dyjzwkPD+eHH354oyp0KQPuhcKxJcpyk8kZT9gALOygwRgYcgpqDgITC7h6ABY3h9/fhZsnsidmSUKWtL02WdImSZJa5GfMG1rZFYL/gZJNofOKNztWzC3YPQOOLwFdsrLOtxU0+BxcSr95rFKWkyVtkiRJkpQbXD+qJGxolLZsb8rODVp+Ax8fBb8PlOOeXw/zA2DNRxB15c3PIUlPyaRNkiRJyh+EgK3jleWKnaFQmZdvnxlOXvDuQhhwQClpEzo49QfMqwIbhislcpL0hmTSJkmSJOUPl7ZCxD4wNocGn2XPOVx8oeNS6LMDvBspVaZHf4FvK8KWzyH2fvacV8oXZNImSZIk5X06rTK7AUD1fmBfNHvPV6QyfPg3dN8I7jUgOV4ZF25uBdj5ldILVZIySSZtkiRJUt53egVEBoOFPdQZ/urts4pnbej5L3RZBa5+yjAhO6cpydu+byHpyduLRcr1ZNImSZIk5W1J8bD9S2W5zghlgNy3SaOBEk2g7y54fwk4l4QnUbB1HHxbCY78rExcL0mvIJM2SZIkKW87/CPEXAe7IlCtr3pxGBlB2bbQ/wC0+QHsi8GjW7BxhNJh4dSfSjWuJKVDJm2SJElS3vXkAeyZpSw3GAumli/f/m0wNoFKXWDQUWg+E6xdlEnp1/SD+TUheJ2cGktKk0zapDxj4sSJWT5x+Ovw9PRkzpw5+vsajYa1a9emu314eDgajUY/+fnryqrjZERiYiI+Pj7s378/28+VW9y7dw8XFxeuX7+udijS8/bOhviHUNAXKnygdjSGTMyhWh8YchIaTwQLB7h7AVZ+CD/Wh9D/ZPImGZBJm6R39+5d+vfvT7FixTA3N8fV1ZXAwED27dundmi52q1bt2jWrFmWHrN79+60bdvWYJ27uzu3bt2iXLlyWXqutCxYsAAvLy9q1qypX6fRaFLdateurX/8yy+/pGbNmlhZWeHg4JDtMb5tzs7OdO3alQkTsmDAVilrRF+HgwuU5cYTwchY1XDSZWYNtYfB0NNQ91Mws4FbJ2Fpe1jcAiIOqB2hlEPIpE3Sa9++PSdOnGDJkiWEhISwbt066tevz/372TeuUGJi3m986+rqirm5ebafx9jYGFdXV0xMTLL1PEII5s2bR69evVI9tmjRIm7duqW/rVu3Tv9YYmIi77//Pv3798/W+LJDUlJShrbr0aMHy5YtIyoqKpsjkjJk5zTQJoBHLSgZqHY0r2ZhDw3HKvOaBnysjCcXsQ8WNYWl78HNk2pHKKlMJm0SAA8fPmTPnj18/fXXNGjQAA8PD6pVq8aYMWNo3bq1wXb9+vWjUKFCWFhYUK5cOTZs2KB/fPXq1ZQtWxZzc3M8PT2ZNWuWwXk8PT2ZMmUKXbt2xc7Ojr59lUbBe/fupU6dOlhaWuLu7s7gwYOJjY19acxfffUVhQoVwtbWll69ehEfH59qm59//hlfX18sLCwoXbo0P/zwQ7rH+/HHHylcuDA6nc5gfZs2bejZsycAYWFhtGnThkKFCmFjY0PVqlX577//Xhrni9Wjhw8fplKlSlhYWFClShVOnDCcYFqr1dKrVy+8vLywtLSkVKlSzJ07V//4xIkTWbJkCf/884++RGvnzp1pVo/u2rWLatWqYW5ujpubG6NHjyY5OVn/eP369Rk8eDCffvopTk5OuLq6MnHixJc+n2PHjhEWFkaLFi1SPebg4ICrq6v+5uTkpH9s0qRJDBs2jPLly7/0+M/z9PTkiy++oGvXrtjY2ODh4cG6deu4e/cubdq0wcbGBj8/P44eParf5/79+3Tq1IkiRYpgZWVF+fLl+eOPPwyOq9PpmD59Oj4+Ppibm1OsWDG+/FLpXZhyHVesWEG9evWwsLBg2bJl6HQ6Jk+eTNGiRTE3N6dixYr8+++/BsctW7YshQsXZs2aNRl+jlI2iTwPJ5cry40nZW5SeLVZO0PglzD4BPh3ByMTCN0KP9aDld3gbojaEUpqEdJriY6OFoCIjo5O9diTJ09EcHCwePLkibJCpxMi4bE6N50uQ88nKSlJ2NjYiKFDh4r4+Pg0t9FqtaJGjRqibNmyYsuWLSIsLEysX79ebNq0SQghxNGjR4WRkZGYPHmyuHjxoli0aJGwtLQUixYt0h/Dw8ND2NnZiZkzZ4rQ0FD9zdraWsyePVuEhISIffv2iUqVKonu3bunG++KFSuEubm5+Pnnn8WFCxfE2LFjha2trahQoYJ+m6VLlwo3NzexevVqcfnyZbF69Wrh5OQkFi9enOYxo6KihJmZmfjvv//06+7fv2+w7uTJk2LBggXizJkzIiQkRHz++efCwsJCREREGDzH2bNn6+8DYs2aNUIIIR49eiQKFiwoOnfuLM6ePSvWr18vihcvLgBx4sQJIYQQiYmJYvz48eLIkSPi8uXLYunSpcLKykqsWLFCf4wOHTqIpk2bilu3bolbt26JhIQEceXKFYPjXL9+XVhZWYkBAwaI8+fPizVr1ghnZ2cxYcIEfWz16tUTdnZ2YuLEiSIkJEQsWbJEaDQasWXLlnSv/TfffCNKly6dav3zz/NlFi1aJOzt7V+5nRDKtXRychILFiwQISEhon///sLOzk40bdpUrFy5Uly8eFG0bdtW+Pr6Ct3T1/r169fFjBkzxIkTJ0RYWJj49ttvhbGxsTh06JD+uJ9++qlwdHQUixcvFqGhoWLPnj3ip59+EkII/XX09PTUv3Zu3rwpvvnmG2FnZyf++OMPceHCBfHpp58KU1NTERISYhBzx44dRbdu3TL0/F5Xqs8YKbVlHYWYYCfEn/9TO5I3dy9UiFW9hZhgrzyniQ5CrOkvRFS42pHlSi/7/s7pZNL2mjKVtCU8Vt5oatwSHmf4Oa1atUo4OjoKCwsLUbNmTTFmzBhx6tQp/eNBQUHCyMhIXLx4Mc39O3fuLJo0aWKwbuTIkaJMmTL6+x4eHqJt27YG2/Tq1Uv07dvXYN2ePXuEkZFRul9KAQEBYsCAAQbrqlevbpC0eXt7i+XLlxtsM2XKFBEQEJDmMYUQok2bNqJnz576+wsXLhSFCxcWWq023X3Kli0rvvvuO/39lyVtCxcuFAUKFDB4XvPnzzdIttIycOBA0b59e/39bt26iTZt2hhs82LS9tlnn4lSpUrpkxkhhPj++++FjY2N/vnUq1dP1K5d2+A4VatWFaNGjUo3liFDhoiGDRumWg8ICwsLYW1trb+llcRlNmn73/+efeneunVLAGLcuHH6dQcOHBCAuHXrVrrHadGihRgxYoQQQoiYmBhhbm6uT9JelHId58yZY7C+cOHC4ssvvzRYV7Vq1VSvw2HDhon69etn6Pm9Lpm0vUL4vqfJjaMQd0NevX1ucfucEH90fvb5PqmAEBtGCBGT/mtfSi03J22yelTSa9++PTdv3mTdunU0bdqUnTt3UrlyZRYvXgzAyZMnKVq0KCVLlkxz//Pnz1OrVi2DdbVq1eLSpUtotc/GHqpSpYrBNqdOnWLx4sXY2Njob4GBgeh0Oq5cuZLuuapXr26wLiAgQL8cGxtLWFgYvXr1MjjuF198QVhYWLrXoEuXLqxevZqEhAQAli1bxgcffICRkfJWefz4MZ988gm+vr44ODhgY2PD+fPnuXr1arrHfDFuPz8/LCws0ow7xffff4+/vz8FCxbExsaGH3/8McPneP5cAQEBaJ6rFqpVqxaPHz826OHo5+dnsJ+bmxuRkZHpHvfJkycG8T9v9uzZnDx5Un9r0qRJpmJOy/PxFSpUCMCgijVlXUrMWq2WKVOmUL58eZycnLCxsSEoKEh//c6fP09CQgKNGjV66Xmff53GxMRw8+bNNF/f58+fN1hnaWlJXFxcZp+mlFWenxS+cldwLqFuPFmpUBn4YBn03g7FG4AuCY78BHMrwtYJECfbUuZ12dtiWVKYWsFnN9U7dyZYWFjQpEkTmjRpwrhx4+jduzcTJkyge/fuWFpmzfhG1tbWBvcfP35Mv379GDx4cKptixUr9lrnePz4MQA//fRTquTO2Dj9HmStWrVCCMHGjRupWrUqe/bsYfbs2frHP/nkE7Zu3crMmTPx8fHB0tKS9957L0s7VPz555988sknzJo1i4CAAGxtbZkxYwaHDh3KsnM8z9TU1OC+RqNJ1a7vec7Ozpw5cybNx1xdXfHx8cm2+FIS0LTWpcQ8Y8YM5s6dy5w5cyhfvjzW1tYMHTpU/z/K6Ov4xddpRkVFRVGwYMHX2lfKAhc2wPUjymdf/dFqR5M9ivpD17VwZTdsmwLXD8O+OXD0V6g5CGr0B3NbtaOUsoFM2t4GjUbp0p0LlSlTRt+I3s/Pj+vXrxMSEpJmaZuvr2+q4UH27dtHyZIlX5ooVa5cmeDg4Ex92fv6+nLo0CG6du2qX3fw4EH9cqFChShcuDCXL1+mS5cuGT6uhYUF7777LsuWLSM0NJRSpUpRuXJlg+fTvXt32rVrByjJYXh4eKbi/v3334mPj9eXVj0fd8o5atasyYABA/TrXiwdNDMzMyi9TO9cq1evRgihT2z27duHra0tRYu+/mTZlSpVYv78+QbHzUn27dtHmzZt+N///gcoyVxISAhlypQBoESJElhaWrJt2zZ69+6doWPa2dlRuHBh9u3bR7169QzOVa1aNYNtz549S/369bPmyUiZo02G/yYpywEDwdZV3Xiym1dd6LUFLm1Rkrc7Z2DHl7B3DhQoDo6eys3BAxy9ni67K+PDSbmSTNokQOlx9/7779OzZ0/8/PywtbXl6NGjTJ8+nTZt2gBQr1496tatS/v27fnmm2/w8fHhwoULaDQamjZtyogRI6hatSpTpkyhY8eOHDhwgHnz5r20xybAqFGjqFGjBh9//DG9e/fG2tqa4OBgtm7dyrx589LcZ8iQIXTv3p0qVapQq1Ytli1bxrlz5yhevLh+m0mTJjF48GDs7e1p2rQpCQkJHD16lAcPHjB8ePoTRnfp0oWWLVty7tw5/Rd/ihIlSvD333/TqlUrNBoN48aNe2mp1Is6d+7M2LFj6dOnD2PGjCE8PJyZM2emOsdvv/1GUFAQXl5e/P777xw5cgQvLy/9Np6engQFBXHx4kUKFCiAvb19qnMNGDCAOXPmMGjQID7++GMuXrzIhAkTGD58uL6693U0aNCAx48fc+7cuUyNCXf16lWioqK4evUqWq1W38vVx8cHGxub147nRSVKlGDVqlXs378fR0dHvvnmG+7cuaNP2iwsLBg1ahSffvopZmZm1KpVi7t373Lu3Lk0hzFJMXLkSCZMmIC3tzcVK1Zk0aJFnDx5kmXLlum3iYuL49ixY0ydOjXLno+UCSd+h/uXwKoA1Exdcp8naTTKcCY+TSB4DeyYCvdD4fYZ5ZZ6B7Ar/Fwy5/n09nTZplDu6mmbz8ikTQLAxsaG6tWrM3v2bMLCwkhKSsLd3Z0+ffrw2Wef6bdbvXo1n3zyCZ06dSI2NhYfHx+++uorQCkxW7lyJePHj2fKlCm4ubkxefJkunfv/tJz+/n5sWvXLsaOHUudOnUQQuDt7U3Hjh3T3adjx46EhYXx6aefEh8fT/v27enfvz9BQUH6bXr37o2VlRUzZsxg5MiRWFtbU758eYYOHfrSeBo2bIiTkxMXL16kc+fOBo9988039OzZk5o1a+Ls7MyoUaOIiYl56fGeZ2Njw/r16/noo4+oVKkSZcqU4euvv6Z9+/b6bfr168eJEyfo2LEjGo2GTp06MWDAADZv3qzfpk+fPuzcuZMqVarw+PFjduzYgaenp8G5ihQpwqZNmxg5ciQVKlTAycmJXr168fnnn2c43rQUKFCAdu3asWzZMqZNm5bh/caPH8+SJUv09ytVqgTAjh07srRk6vPPP+fy5csEBgZiZWVF3759adu2LdHR0fptxo0bh4mJCePHj+fmzZu4ubnx0UcfvfS4gwcPJjo6mhEjRhAZGUmZMmVYt24dJUo8azP1zz//UKxYMerUqZNlz0fKoMRY2Kl8FlH3U7CwUzeet83ICMq1hzJt4d4lZVqsB+HwIOXv01tSLMTcUG4RaQycbmIJDsUMEzl9guchq11VphFCzpHxOmJiYrC3tyc6Oho7O8MPh/j4eK5cuYKXl1e6DbYlKTc7ffo0TZo0ISwsLEtLyXK7GjVqMHjw4FTJflaTnzFp2D0Dtn+hJBcfH5FVgGkRAuLuGyZxD8KfJXjR10G8oubAyvlZMvdiSZ1dUWVe1RzuZd/fOV3Ov7qSJOU4fn5+fP3111y5ciVTg+XmZffu3ePdd9+lU6dOaoeS/8Teh71PB6BuOE4mbOnRaJSBe62doWiV1I9rk5TE7cVkLqXE7kkUxN1TbjeOpXF8Y6XNXFrVro5eYOkoq17fkEzaJEl6La+q9s5vnJ2d+fTTT9UOI3/aMxMSH4Grn1JFKL0eY1Nw8lJuaYmPVpK3F5O5lARPm/hs/ZVdqfc3s02n2tVTqZI1laXGryKTNkmSJCn3ehAOh39SlptMUtp2SdnDwh7c/JTbi3Q6eHw7dTu6lATv0S0lsb5zRrmlxdYtdTJXrDo4FU97+3xIJm2SJElS7rX9S2WQ2eINwLuh2tHkX0ZGSq9Uu8LgUTP140lP4OG1dKpewyHxsZLYPboFVw8826/p11Dj5Z2E8hOZtGUj2cdDkqTsID9bnrp1Cs6sVJYbT1Q1FOkVTC2hYEnl9iIhlNkcHoTDw3DDqtdCZd5unDmcTNqyQcpo7XFxcVk2i4AkSVKKlGmyXpzNIt/5b6Lyt9x7ULiimpFIb0KjAesCyq2ov9rR5GgyacsGxsbGODg46OdCtLKyypEjx0uSlLsIIYiLiyMyMhIHB4eXzjSS54XtgLDtYGQKDd9s7EFJyi1k0pZNXF2V6VNeNvG2JEnS63BwcNB/xuRLOt2zUraqvdLv7ShJeYxM2rKJRqPBzc0NFxcXkpKS1A5HkqQ8wtTUNH+XsAGc+xtunVSGkKg7Uu1oJOmtkUlbNjM2NpYfsJIkSVklORG2T1GWaw1RBoqVpHxCDmgjSZIk5R7HFik9C61dIGCA2tFI0lslkzZJkiQpd4iPgV1fK8v1R4OZtbrxSNJbJpM2SZIkKXc4ME+Z8LyAD1TuqnY0kvTWyaRNkiRJyvke3YH985TlRuOVeTIlKZ+RSZskSZKU8+36GpJioUgV8G2tdjSSpAqZtEmSJEk5271QOLZYWW4ySRlBX5LyIZm0SZIkSTnb9skgtFAiEDxrqx2NJKlGJm2SJElSznX9KAT/A2jkpPBSvieTNkmSJClnEgK2TlCWK3aGQmXUjUeSVCaTNkmSJClnurQVIvaCsTnUH6N2NJKkOpm0SZIkSTmPTvtsUvjq/cDBXdVwJCknkEmbJEmSlPOcXgGR58DCHmoPUzsaScoRZNImSZIk5SxJ8bD9S2W5zgiwclI3HknKIWTSJkmSJOUsR36CmOtgVwSq9VU7GknKMWTSJkmSJOUcTx7A7pnKcoPPwNRS3XgkKQeRSZskSZKUc+ydA/EPoaAvVOikdjSSlKPIpE2SJEnKGaJvwKEFynLjiWBkrGo4kpTT5Iik7fvvv8fT0xMLCwuqV6/O4cOHX7r9w4cPGThwIG5ubpibm1OyZEk2bdqkf3z+/Pn4+flhZ2eHnZ0dAQEBbN68Oc1jCSFo1qwZGo2GtWvXZuXTkiRJkjJj51RIjodiNaFkoNrRSFKOY6J2ACtWrGD48OEsWLCA6tWrM2fOHAIDA7l48SIuLi6ptk9MTKRJkya4uLiwatUqihQpQkREBA4ODvptihYtyldffUWJEiUQQrBkyRLatGnDiRMnKFu2rMHx5syZg0ZOPixJkqSuyPNwcrmy3GSynBRektKgEUIINQOoXr06VatWZd68eQDodDrc3d0ZNGgQo0ePTrX9ggULmDFjBhcuXMDU1DTD53FycmLGjBn06tVLv+7kyZO0bNmSo0eP4ubmxpo1a2jbtm2GjhcTE4O9vT3R0dHY2dllOA5JUs3NE+DoCZaOakciSakt/wBCNoNvK+i4VO1opDwsN39/q1o9mpiYyLFjx2jcuLF+nZGREY0bN+bAgQNp7rNu3ToCAgIYOHAghQoVoly5ckydOhWtVpvm9lqtlj///JPY2FgCAgL06+Pi4ujcuTPff/89rq6ur4w1ISGBmJgYg5sk5RpXD8KP9eH76nD1kNrRSJKhiP1KwqYxhkYT1I5GknIsVZO2e/fuodVqKVSokMH6QoUKcfv27TT3uXz5MqtWrUKr1bJp0ybGjRvHrFmz+OKLLwy2O3PmDDY2Npibm/PRRx+xZs0aypR5NtnwsGHDqFmzJm3atMlQrNOmTcPe3l5/c3eXU6pIuciFDcrfx3dgcQs4ukjdeCQpxfOTwlfuCs4l1I1HknKwHNERITN0Oh0uLi78+OOP+Pv707FjR8aOHcuCBQsMtitVqhQnT57k0KFD9O/fn27duhEcHAwopXXbt29nzpw5GT7vmDFjiI6O1t+uXbuWlU9LkrJX2A7lr3Mp0CXBhqGwbjAkJ6galiRxYQNcPwymVlA/dZMYSZKeUTVpc3Z2xtjYmDt37hisv3PnTrpVlm5ubpQsWRJj42ddwX19fbl9+zaJiYn6dWZmZvj4+ODv78+0adOoUKECc+fOBWD79u2EhYXh4OCAiYkJJiZKf4z27dtTv379NM9rbm6u742acpOkXOHRbbhzFtBAj01Pq580cHwJLG4JMbfUjlDKr7TJ8N8kZTlgINi+uqmKJOVnqiZtZmZm+Pv7s23bNv06nU7Htm3bDNqfPa9WrVqEhoai0+n060JCQnBzc8PMzCzdc+l0OhISlFKF0aNHc/r0aU6ePKm/AcyePZtFi2S1kZTHpJSyFa4I1s5QZzh0WaVMxH39sNLW7drLh9mRpGxxcincvwSWTlBzsNrRSFKOp3r16PDhw/npp59YsmQJ58+fp3///sTGxtKjRw8AunbtypgxY/Tb9+/fn6ioKIYMGUJISAgbN25k6tSpDBw4UL/NmDFj2L17N+Hh4Zw5c4YxY8awc+dOunTpAoCrqyvlypUzuAEUK1YMLy+vt/jsJektCHv6o8i74bN1JRpDnx3KqPOPb8Oi5rKdm/R2JcbCjmnKcr1PwULWXkjSq6g+TlvHjh25e/cu48eP5/bt21SsWJF///1X3znh6tWrGBk9yy3d3d0JCgpi2LBh+Pn5UaRIEYYMGcKoUaP020RGRtK1a1du3bqFvb09fn5+BAUF0aRJk7f+/CRJVTrds5K255M2gALe0Ps/+GcABP+jtHO7dQqaTQeT9EutJSlLHJyv/GBw8IAqPdWORpJyBdXHacutcvM4L1I+cusULKwLZjbw6ZW0kzEhYO83sG0KIMC9OnT4TbYvkrJP7H34tiIkxMC7P4Pf+2pHJOUjufn7W/XqUUmSslHo06pRzzrpl55pNFBnBHT5S2nndu0QLKwn27lJ2WfPTCVhc/WDcu3VjkaScg2ZtElSXha2Xfn7YtVoWko0Sd3O7djibA1PyocehMPhn5TlJpPASH4NSVJGyXeLJOVVibHKTAgAPo0ytk8Bb+i9VZlKSJcE64fA+qGQnPjKXSUpQ7Z/qby2itfP2I8JSZL0ZNImSXlV+F7ly9GhGDgVz/h+5rbQ4XdoOA7QwLFFsKSlMt6bJL2JW6fgzEplufEkdWORpFxIJm2SlFc9XzWq0WRuX40G6n4CnVeC+dN2bj/Wh2tHsjxMKR9JGUi33HvKuIGSJGWKTNokKa/SJ20ZrBpNS8l3oO8OKFgaHt2Cxc3h2JKsiU/KXy7vVMYMNDKFhp+rHY0k5UoyaZOkvOjhNbgXAhpj8Kr7ZsdKGc/NtxVoE2H9YNgwXLZzkzJOp3s2KXzVXuAkBzGXpNchkzZJyotSStmKVgFLhzc/nrktvP/b0xISDRz9BZa0gkd3XrmrJBG8Bm6dBDNbqDtS7WgkKdeSSZsk5UVpTV31poyMlC/cziuetnM7CD/Wg+tHs+4cUt6TnAjbJivLtQYr899KkvRaZNImSXmNTqu0H4LsGVKhZKDSzs25lNLObVEzOP5b1p9HyhuOLVbGZrN2gYCBr9pakqSXkEmbJOU1N09AfLQyu0HhytlzjgLe0GcblG6ptHNbN0i2c5NSS3gEu75WluuPBjNrdeORpFxOJm2SlNekTF3lVQ+MTbLvPCnjuTV4rp3bb61lOzfpmf3fQdw9KOADlbuqHY0k5XoyaZOkvCYzU1e9KSMjqJfSzs0Orh5QxnO7fiz7zy3lbI/uwP55ynKj8WBsqm48kpQHyKRNkvKS+Gi4/nQA3Lc5RVDJQGXeUudS8OgmLGoKx39/e+eXcp5dX0NSLBSpAr6t1Y5GkvIEmbRJUl5yZTcIrVId5ejxds/t7KOM56Zv5/YxbBwh27nlR/dClQ4IoEwKn9kZOSRJSpNM2iQpL3mbVaNpsbB72s5trHL/yM9KO7fHkerEI6lj+xTlx0OJQPCsrXY0kpRnyKRNkvKSrJi66k0ZGUG9T6HTc+3cFtaT7dzyi+vHIHgtoIHGE9SORpLyFJm0SVJecT9MGQ/LyDRnlG6Uagp9toNzyaft3JrBiaVqRyVlJyFg63hluWJnKFRW3XgkKY+RSZsk5RUppWzFaoC5jbqxpHAuAb23QakWoE2AfwbCxk9Am6R2ZFJ2CP0PIvaCsTnUH6N2NJKU58ikTZLyirAdyl/vBurG8SILO+i4FOp/ptw/8hMske3c8hyd9tmk8NX7goO7uvFIUh4kkzZJygu0SUrPUVCvE8LLGBlB/VHQ6c+n7dz2K+O53ZDt3PKM0ysh8pwyE0ft4WpHI0l5kkzaJCkvuH4EEh+BVQFwraB2NOkr1Uxp51agBMTcgF+bwYllakclvamkeNjxpbJcezhYOakbjyTlUTJpk6S8IGXqquINlFKtnMy5hDJvaanmT9u5DYBNI2U7t9zsyE8QfQ3sikD1fmpHI0l5Vg7/dJckKUPUHp8tsyzsoeOyZ43VD/8Iv7WBx3fVjUvKvCcPYfdMZbnBZ2BqqWo4kpSXyaRNknK7uCi4eUJZzi1JGzxt5zYaPvgDzGwhYh/8WA9uHFc7Mikz9s6G+IdQ0BcqdFI7GknK02TSJkm53eUdgACXMmDnpnY0mVe6+Qvt3JrCyeVqRyVlxL1QODhfWW48EYyMVQ1HkvI6mbRJUm6X26pG01KwpNLOrWQzpZ3b2v6w6VPZzi0n0+lg3SDl/+XdCEoGqh2RJOV5MmmTpNxMiOfGZ8vFSRso7dw+WA71Riv3Dy+E39rKdm451dFflKFbTK2h1Rw5KbwkvQUyaZOk3OzuRaVK0cQCPGqqHc2bMzKCBmOUTgpmtsro+j/Wl+3ccpqH1+C/icpy44ngUEzNaCQp35BJmyTlZilVox4181avPd+WSnVpAR+Iuf60ndsfakclgVK6u2EYJD4G9xpQtbfaEUlSviGTNknKzfJCe7b0FCyldFAo2fRpO7ePYPMo2c5NbadXQuhWMDaD1t/l/HEBJSkPke82ScqtkuIhfK+ynBeTNnjazu0PqPupcv/QAtnOTU2P78K/o5TleqOUDiSSJL01MmmTpNzq2kFIfgI2rspwH3mVkRE0HPu0nZuN0s5tcQtIjFU7svxn86fw5AG4lodaQ9SORsqj4pO0HLx8n3nbL3EtKk7tcHIUE7UDkCTpNaVMXeXdMH/03PNtCc7bYUlruHcRgj6DVnPVjir/uLARzv0NGmNoPQ+MTdWOSMojomITORoexdGIBxwJj+LsjWiStAKAAjbmdKomO7qkkEmbJOVWeWWoj8woWArefToUyLHF4NMYfFupHVXe9+QhbBiuLNcaDIUrqhlNtvjj8FX2XrpHaVdbyhW1p3wRe5xtzNUOK88RQnA1Ko4j4Q84Gh7FkfAowu6mLjV3sTWnqqcTRRzyUAerLCCTNknKjR7dgTtnlGXvBurG8rYVrw81B8H+b5XBXYv4g11htaPK27aOg8e3ld689UapHU2WC74Zw9g1Z9AJ2Hjmln59YXsLyhVRErjyTxO5AjKRy5RkrY7gWzHPJWkPuPc4IdV2JVxsqOLpSBUPJ6p6OuHuZIkmP9QgZJJM2iQpN7r8tJTNrQJYO6sbixoajoMru+DWKVjTDz78R/ZizC6Xd8Lx35Tl1t/lraFlUEp+Jq0/h05AFQ9HijhacuZGNJfvxnIzOp6b0fFsCb6j376wvYU+gUtJ6GQi90xsQjInrj7kSHgURyOiOHH1IXGJWoNtTI01+BV1oIqnI1U9nPD3cMTR2kyliHMXmbRJUm6Ul4f6yAgTM2j/CyysC1d2K6VutYeqHVXekxgL6592OKjaO28M4PyCjWducehKFOYmRsz5oCJFHa0AeBSfxLmbMZy9Ec3p69GcvRHN5XvPErmgc88SuSIOlpQrYodfUQd9IueUT5KQyJh4fVu0o+EPCL4Vg1YnDLaxtTChiocjVTyVUjS/ovZYmMp5al+HTNokKbfR6Z5rz9ZI3VjU5FwCmn4F6wfD9ingVReKVFY7qrxlx1R4EA52RaHRBLWjyXJPErVM3XgegI/qeesTNgBbC1NqFC9AjeIF9OtSErkz16M5c+NZInfj4RNuPHySKpF7vlq1fBH7XF+aJIQg7O7jp1WdDzgaEUXE/dS9O4s4WCpVnZ5OVPV0pKSLLUZGsqozK8ikTZJymztnITZSmfPRvbra0airclcI/Q/Or4PVvaHfbjC3UTuqvOH6UTj4g7Lcag5Y2KkaTnaYvyuMm9HxFHGw5KN63q/cPq1ELiY+iXM3npbIPU3krjyXyP177rZ+2yIOlvgVfVatmtMTucRkHWduROvboh2LiOJBnOHg1hoNlHa1o+rTJK2KhyOFZeeBbCOTNknKbVKqRr3qKNWE+ZlGowz7ceMYRIXBv6OhzTy1o8r9khPhn49B6MDvAyjRRO2Isty1qDgW7goD4LPmvliavV51nZ2FKQHeBQjwNkzkzj5N4M48TeieT+Q2n32WyBV1tNS3j/N7WirnYKXO+zr6SRLHrz7rMHDq2kMSknUG25ibGFHR3YGqnk5U8XSksocjdhZy+Je3RSZtkpTb5Pf2bC+ycoJ2C2FJKzjxu5JglGmjdlS5295v4O55sHKGptPUjiZbTN10noRkHTWKO9G8vGuWHtvOwpSa3s7U9H7WSSj6SRLnbkYbtJELvx/H9QdPuP4gdSL3YolcdiRyNx4+UcZHC1fapF288whh2BwNJ2sz/D0c9SVp5QrbY2YiO/2oRSZtkpSbJMbC1QPKskzanvGqA7WHKcnGusHKMCD2RdWOKne6Ewy7ZyrLzWcoSXEesz/0HpvP3sZIAxNalX0rQ0vYW6aTyN1Q2sel3CKeS+Q2nXmWyLk7PW0jV8RBn8jZW2W8hEurE4TceaQvRTsaHsXN6PhU23kWsNK3Ravi6URxZ2s59EYOIpM2ScpNIvaDNhHsiyljZknPNPhMGZ7i5nH4ux90WwdGsodapui08M9A0CVBqRZQtp3aEWW5ZK2OSeuDAfhfDQ983dRrq2dvaUpNH2dq+qSdyKW0kYu4H8e1qCdcizJM5Io5WRlUrZYr/CyRi0/ScuraQ33PzmMRD3gUn2xwfmMjDWUL2z0dG80Rf09HXGwt3s6Tl16LTNpyopsnlbkk83t7JSk1/dRVDfLH1FWZYWwK7X+GBXWU+Un3zYE6I9SOKnc5OF9Jes3tocWsPPkaW3boKhfvPMLBypThTXLehPdpJnJxSZy9+VyJ3PVorkbF6W/PDwhczMkKRytTgm/F6KeCSmFtZkylYo7K+GieTlR0d8DaXKYBuYn8b+U0f/eF0yugzQ9QqYva0Ug5jWzP9nIFvJUqvX8GKMNVeNWHov5qR5U7RF2G7V8oy+9MATs3dePJBlGxiczachGAEe+UUq3Bf2bZW5lSy8eZWmkkcint487ceD6RU7YpaGtOtacdBqp6OlHa1RYTY9keLTeTSVtO41JG+btvLlToJEd5l56Jvq5MlK4xguL11I4m56rYGUK3wrk1sLoXfLQHzG3VjipnE0JpC5j8BDzrKEOp5EGztlwkJj6Z0q62dM7lk5Cnlcg9jEvk7I0YouISqVjUQU4FlQfJjCCnqdIDzO2UL+dLQWpHI+UkKaVsRfzB0lHdWHIyjQZazlYGhH1wBTbnvbkys9zx3yB8D5hYQutv82S16Lmb0Sw/fBWAia3LYpwHB3t1sDKjdglnWlcoTLECVjJhy4NeO2k7duwYS5cuZenSpRw/fjwrY8rfLOyhSk9lee8cVUORchhZNZpxlo7Q/ielVPLkMji7Wu2Icq6Ym7Dlc2W50ThwKq5uPNlACMGkdcEIAS383AwGx5Wk3CTTSVtkZCQNGzakatWqDB48mMGDB1OlShUaNWrE3bt3XyuI77//Hk9PTywsLKhevTqHDx9+6fYPHz5k4MCBuLm5YW5uTsmSJdm0aZP+8fnz5+Pn54ednR12dnYEBASwefNmg2P069cPb29vLC0tKViwIG3atOHChQuvFX+Wq9EfjM3g2kG4elDtaKScQKdVekZC/p66KjM8aj7riLB+GDy8qm48OZEQsHEEJMQoJbjVP1I7omyx/vQtDodHYWFqxGfNfdUOR5JeW6aTtkGDBvHo0SPOnTtHVFQUUVFRnD17lpiYGAYPHpzpAFasWMHw4cOZMGECx48fp0KFCgQGBhIZGZnm9omJiTRp0oTw8HBWrVrFxYsX+emnnyhSpIh+m6JFi/LVV19x7Ngxjh49SsOGDWnTpg3nzp3Tb+Pv78+iRYs4f/48QUFBCCF455130Gq1mX4OWc7WFSp8oCzvm6tuLFLOcPMkPHmg9OorIhvWZ1i9UVCkCiREK8OA6HLA+zsnOfc3XNwERqbQel6eHCIlLjGZaZuU+UX71/OhiJxiScrNRCbZ2dmJw4cPp1p/6NAhYW9vn9nDiWrVqomBAwfq72u1WlG4cGExbdq0NLefP3++KF68uEhMTMzUeRwdHcXPP/+c7uOnTp0SgAgNDc3Q8aKjowUgoqOjMxVHht0NEWKCvRAT7ISIvJA955Byj53TldfCn13UjiT3uR8mxJeFleu3c7ra0eQcj+8J8XVx5brsSPvzNi+YGXRBeIzaIGpO2yaeJCarHY6UA2T793c2ynRJm06nw9Q09SjMpqam6HS6NPZIX2JiIseOHaNx48b6dUZGRjRu3JgDBw6kuc+6desICAhg4MCBFCpUiHLlyjF16tR0S8i0Wi1//vknsbGxBAQEpLlNbGwsixYtwsvLC3d39zS3SUhIICYmxuCWrZxLQOkWyvK+b7P3XFLOJ9uzvT6n4sqYYwA7p8G1lze/yDeCxkDcPaXHeu3hakeTLa5FxbFw92UAPm/hi4Vp3itJlPKXTCdtDRs2ZMiQIdy8eVO/7saNGwwbNoxGjTLX1ubevXtotVoKFSpksL5QoULcvn07zX0uX77MqlWr0Gq1bNq0iXHjxjFr1iy++OILg+3OnDmDjY0N5ubmfPTRR6xZs4YyZcoYbPPDDz9gY2ODjY0NmzdvZuvWrZiZpT1uz7Rp07C3t9ff0kvuslStocrf0yuUxsJS/hQfA9efJhoyaXs9fh2h3HsgtLC6t3JN87OQLcrnisZIqRbNowN5f7ExmMRkHTW9C9C0XNbOLypJash00jZv3jxiYmLw9PTE29sbb29vvLy8iImJ4bvvvsuOGA3odDpcXFz48ccf8ff3p2PHjowdO5YFCxYYbFeqVClOnjzJoUOH6N+/P926dSM4ONhgmy5dunDixAl27dpFyZIl6dChA/HxqediAxgzZgzR0dH627Vr17LtOeq5VwWPWsqUMgd/yP7zSTlT+B7QJYOTNzh6qh1N7qTRQMtvlOm/HkbAppFqR6Se+BjYMExZrjEgzw4+vPfSPYLO3cHYSPPW5heVpOyW6cF13d3dOX78OP/995++t6Wvr69BFWdGOTs7Y2xszJ07dwzW37lzB1fXtH8Vubm5YWpqirHxs2JuX19fbt++TWJior6kzMzMDB8fZW5Gf39/jhw5wty5c1m4cKF+v5RSsxIlSlCjRg0cHR1Zs2YNnTp1SnVec3NzzM3NM/0c31itIRCxD44uhjqfgKXD249BUpd+6ipZyvZGLOyVYUAWNYPTf4JPY/B7X+2o3r5tkyDmuvIDoMFYtaPJFklaHZPWKx3PPqzhQSlXObiylDdkqqQtKSkJExMTzp07R5MmTRg0aBCDBg16rYQNlMTK39+fbdu26dfpdDq2bduWbvuzWrVqERoaatB+LiQkBDc3t3SrNlOOm5CQkO7jQgiEEC/dRhUl3lHanCQ+gqO/qh2NpAbZni3rFKsBdT9VljcOhwfhqobz1kXshyM/K8utvwMzK3XjySZLD0ZwKfIxjlamDGuc8+YXlaTXlamkzdTUlGLFimXpsBjDhw/np59+YsmSJZw/f57+/fsTGxtLjx49AOjatStjxozRb9+/f3+ioqIYMmQIISEhbNy4kalTpzJw4ED9NmPGjGH37t2Eh4dz5swZxowZw86dO+nSRZnL8/Lly0ybNo1jx45x9epV9u/fz/vvv4+lpSXNmzfPsueWJTQaqPl0KJWD8yEp7epbKY+KuqyM6m9kAl511I4mb6g7EtyrK2OT/d0XtMlqR/R2JD2Bfz5Wlit3A6+66saTTe4/TuCbrSEAfBJYCnur1B3nJCm3ynSbtrFjx/LZZ58RFRWVJQF07NiRmTNnMn78eCpWrMjJkyf5999/9Z0Trl69yq1bt/Tbu7u7ExQUxJEjR/Dz82Pw4MEMGTKE0aNH67eJjIyka9eulCpVikaNGnHkyBGCgoJo0qQJABYWFuzZs4fmzZvj4+NDx44dsbW1Zf/+/bi4uGTJ88pS5d9TpuSJjVSqdaT8I6WUzb26nD8zqxibwLs/KtPFXTsEe2aqHdHbsfMriAoDWzdoMlntaLLNzC0hPIpPpoybHR9Uzd3zi0rSizRCCJGZHSpVqkRoaChJSUl4eHhgbW1t8Hh+mdIqJiYGe3t7oqOjsbOzy/4THvgegj5TGqN/fCRPDoIppeHPLnBhAzT8XCkhkrLO6b/g795KD8oem5Wq07zq5gn4qZHSe/aDP6B0DqtRyCJnb0TTat5ehICV/QKo5uWkdkhSDvTWv7+zUKY7IrRt2zYbwpBeqXI32DVd+aV8YSOUaa12RFJ20ybBld3Kspy6Kuv5vQ+hW5WhL1b3gf57lc4KeY02Cf4ZpCRsZd/NswmbEIKJ684hBLSqUFgmbFKelOmkbcKECdkRh/Qq5jZQrQ/sngH75oBvK6W9m5R3XT+qtLuydAK3CmpHkzc1n6nM7/swAjYMh/Y/57331b65cOcMWDpCs+lqR5Nt1p26ydGIB1iaGjOmWWm1w5GkbJHpNm1Hjhzh0KFDqdYfOnSIo0ePZklQUjqq9QMTC7hxTBkGRMrb9L1GG8jq8OxiYQftfwGNMZxdpZS65SV3Q2DX18py06/BpqC68WST2IRkpm1ShqAa2MCbwnJ+USmPynTSNnDgwDQHlr1x44ZBD04pG9gUhIpKD1j2zlE1FOktkEN9vB3uVaH+045MGz+BqCvqxpNVdDpY9zFoE8GnCfh1UDuibPPDzlBux8Tj7mRJ7zrF1Q5HkrJNppO24OBgKleunGp9pUqVUs04IGWDmh8rDadDt8Lts2pHI2WXuCi4+bRTT/EG6saSH9QZAcVqKuMh/t1HaQeW2x35Wekda2YDLWfnvWrfpyLux/LTbiXR/rxFGTm/qJSnZTppMzc3TzWDAcCtW7cwMcl0Ezkps5yKQ5k2yvJ+OZF8nnVlFwgdFPQF+yJqR5P3GRk/HQbEHq4fUTr95GYPIuC/icpyk0ng8BbmSlbJFxvPk6jVUdvHmXfKFHr1DpKUi2U6aXvnnXf083CmePjwIZ999pl+HDQpm9Uaovw9swoeXlU3Fil7yKmr3j4Hd2g1W1neM1OZPSA3EgI2DIWkWKX00L+n2hFlm90hd9kanDK/aBk5v6iU52U6aZs5cybXrl3Dw8ODBg0a0KBBA7y8vLh9+zazZs3KjhilFxWuBF71lC78B+RE8nmOEBC2Q1mWSdvbVa49VOislHL+3ReePFQ7osw79YfSHtLYXJmqyijTH/O5QpJWx+QNSpOcrgEelCgkB5+W8r5Mv5uLFCnC6dOnmT59OmXKlMHf35+5c+dy5swZ3N3zbhF8jpNS2nZ8idL+Sco77l1SJvQ2NgePmmpHk/80nw6OXhB9TSmxytz44+p6dAf+fTrtX4Mx4OyjbjzZ6LcDEYRGPsbJ2oyhcn5RKZ94rUZo1tbW9O3bN6tjkTLDuyG4lofbZ5QGx/U+VTsiKauEPa0a9QjIsxN652jmtsowIL++A+fWKD0vK3VRO6qM2TwS4h8q4/oFDFI7mmxz73ECc57OLzoysBT2lnJ+USl/eO2eA8HBwVy9epXExESD9a1by5H63wqNBmoNhdW94NACCPhYfsHnFfqhPuQsCKop6g/1x8D2KbBppDLFVQFvtaN6ueB1EPyPMuZc63nKHKt51MygizxKSKZcETs6VJE1PFL+kel39eXLl2nXrh1nzpxBo9GQMnVpSgNQrVabtRFK6SvTFrZNVkZzP7lMmTFByt2SEyB8r7Is27Opq/YwpW1hxF5Y3Rt6bQHjHFqi8+QBbPpEWa49FNz8VA0nO525Hs2Ko8pYoRNblcXYSHY+kPKPTLdpGzJkCF5eXkRGRmJlZcW5c+fYvXs3VapUYefOndkQopQuYxOo+bQKZP93oE1WNx7pzV09CElxYFMICpVVO5r8zcgY3l0IFg7KmHk7pqodUfq2fA6P74BzSaibd5tKCCGYsO4sQkDbioWp4innF5Xyl0wnbQcOHGDy5Mk4OztjZGSEkZERtWvXZtq0aQwePDg7YpRepmIXsCqglLad/0ftaKQ39fwsCHL4AvXZF4VWc5XlvbPhyh5140lL2HY4sRTQKL1FTS3UjijbrD15g+NXH2JlZszoZr5qhyNJb12mkzatVoutrdK12tnZmZs3bwLg4eHBxYsXszY66dXMrJQ5SUGZ2io39XSTUpNTV+U8ZdtCpQ8BAWv65aze2gmPYf3TnuTV+ipt7/Koxwbzi/rgap93k1NJSk+mk7Zy5cpx6tQpAKpXr8706dPZt28fkydPpnhxOeebKqr1AVMruH0aLu9UOxrpdT2OVP6HIKeuymmafgVO3hBzQ0mScsqPo+1fKANs27tDo/FqR5Otvt8RSuSjBIo5WdGrtpfa4UiSKjKdtH3++efodDoAJk+ezJUrV6hTpw6bNm3i22/ltEqqsHKCyl2V5X1zVA1FegMpCberH9gUVDUU6QXmNtD+ZzAygfPr4MTvakcE1w4rPccBWs1RYsyjwu/F8sseZX7RcS3l/KJS/pXppC0wMJB3330XAB8fHy5cuMC9e/eIjIykYUNZpaOaGgOUrv6Xd8LNk2pHI70OOXVVzlakMjQcpyxvHgX3QtWLJTkB/vkYEMoMDj6N1YvlLfhiYzCJWh11SjjT2NdF7XAkSTVZMr+Jk5OTnPNNbY4eyhQ8APvmqhuLlHlCyPZsuUHNweBVV+nhu7oXJCe+ep/ssHsm3LsI1gUh8Et1YnhLdl6M5L/zkZjI+UUlKfNJW3x8PDNmzKB58+ZUqVKFypUrG9wkFdV62ns3eC1EXVE1FCmT7pyD2EilbWIebkye6xkZQbuFYOkIt07Cji/efgy3z8Leb5Tl5jOV5hF5VGLys/lFu9X0xMdFzi8q5W+ZHly3V69ebNmyhffee49q1arJXz05iWt5pZok9D84MA9azFI7IimjUqau8qwNJubqxiK9nF1hZWiNFf+Dfd8qM1cUr/d2zq1NhnUfgy4ZSreEMm3eznlV8tuBcC7fjcXZxowhjUuoHY4kqS7TSduGDRvYtGkTtWrVyo54pDdVa4iStJ1YqkzDY+2sdkRSRsipq3IX31bg3x2OLVaGAem//+2UeB38Hm6eAAt75UdZHv7RfPdRAnP/uwQo84vaWeTQ2Sgk6S3KdPVokSJF9OO0STmQZx0oXBmS4+HQQrWjkTIiMQ4iDijLsj1b7hE4FQqUgEe3YN2g7B8G5H7Ys1kZAqeCrWv2nk9lM4Iu8CghGb+i9rzvL+cXlSR4jaRt1qxZjBo1ioiIiOyIR3pTGo1S2gZw+Edl8E0pZ4vYD9oEsCsKzrIKKNcws4b3fgEjU7iwQSl1yy46HawbrPwYK15fmQklDzt17SErj14HYEKrshjJ+UUlCXiNpK1KlSrEx8dTvHhxbG1tcXJyMrhJOYBvK2Ug0PiHOWM8KenlUqpGfeTUVbmOWwVoPEFZ/ncM3A3JnvMcX6xMXG9qpUyrlYdfJzqdYOL6cwC8W6kI/h6OKkckSTlHptu0derUiRs3bjB16lQKFSokOyLkREbGykTyG4bCge+ham8wlu1Bciw51EfuVmOgMsbe5R2wuif03pa1nUmib8CWp7MdNBoPjp5Zd+wcaM2JG5x4Or/oqGal1Q5HknKUTCdt+/fv58CBA1SoUCE74pGySoVOSvuX6Gtw9m+o0FHtiKS0RN+Au+dBYwReb6kHopS1jIyg7XyYXxNun4Ftk7Nu7DQhYMMwSHwERasq84vmYY8TkvnqX2V+0UENS1DITs4vKknPy3T1aOnSpXny5El2xCJlJVMLqPGRsrxvbs6ZK1EydHmH8rdw5Tw93laeZ+cGbb5Xlg/Me1Z6+qbOroZLQWBsBq3nKaXoedh32y9x91ECngWs6FnbU+1wJCnHyXTS9tVXXzFixAh27tzJ/fv3iYmJMbhJOUiVnmBmA5HnlGFApJxHTl2Vd5RuDlV6KctrPoLYe292vNh7sPlTZbnuSHDJ21WFV+7F8uveZ/OLmpvk7QRVkl5HppO2pk2bcuDAARo1aoSLiwuOjo44Ojri4OCAo6NsMJqjWDoqY0kB7J2jZiRSWnTaZyVtMmnLG975ApxLweM7ytygb1LCvXkUxN2HQuWg1tAsCzGnmrIhmCStoF7JgjQsLecXlaS0ZLpN244dO7IjDim71BigjNcWsReuH4WiVdSOSEpx6xQ8eQDmdvL/kleYWSnDgPzUEEI2w9FflI5AmXVxM5xdpbR1bP0dmJhlfaw5yI4LkWy/oMwvOl7OLypJ6cp00lavnmwsnavYFwG/DnByGeybAx2Xqh2RlCJl6iqvurJ3b17iWh4aT4KgMRA0FjxqZ65qMz4aNgxXlgM+hiJ5e07nxGQdU57OL9qjlifeBW1UjkiScq5MV49KuVDNpxPJn98A90LVjUV6JkxWjeZZ1T9SpiRLjofVvSApPuP7bp0Aj26CU3FlKro8bvH+K1y+F4uzjTmDG8nBpSXpZWTSlh+4lIaSzQAB+79VOxoJIOERXDukLMukLe9JGQbEyhnunIVtkzK235U9cGyRstzqW6W6NQ+LfBTPt9uUH5KfNi2FrZxfVJJeSiZt+UXK1Fan/oBHd9SNRVK+nHXJ4OgFTl5qRyNlB9tC0PYHZfngD3DpFT24E+Ng/dNScf8e4FUne+PLAab/e5HHCclUKGrPe5WLqh2OJOV4MmnLLzwCwL06aBPh0Hy1o5H0U1c1UjcOKXuVDHw2IO7a/vD4bvrb7pwGUZfBtjA0yWDJXC524uoDVh1T5hed2FrOLypJGSGTtvwkpbTtyK8QL8fUU5Wcuir/aDIZXMpAbCT8MyDtYUBuHFMG5QVoORss7N9ujG+ZMr+o0vmgfeWiVComh4uSpIzIsqTt/PnzFC9ePKsOJ2WHks2UMaQSouHYYrWjyb8ehENUGBiZgGferwLL90wtof0vYGwOl7bA4Z8MH09OhH8GgdBB+fehVFN14nyLVh+/zqlrD7E2M2ZU01JqhyNJuUaWJW2JiYlERERk1eGk7GBkBLWetpk5+AMkJ6gbT36VUspWtBpY2Kkbi/R2FCoD70xRlrd8DnfOPXts3xxl1hKrAtD0K1XCe5sexSfx9b8XARjcqAQucn5RScqwDI/TNnz48Jc+fvfuS9pqSDlH+fdh+xfw6Bac+Qsq/U/tiPIfOXVV/lStrzKd3KUtsLo39NkODyJg13Tl8WbTwdpZ3Rjfgu+2h3LvcQLFna3pUUt2wpGkzMhw0jZ37lwqVqyInV3aJQOPHz/OsqCkbGRirsySsHWcMpF8hc5KCZz0dmiT4cpuZVkmbfmLRgNtfoD5NSEyWClxu3UKdElQsimUa692hNku7O5jFu17Nr+omYn87JGkzMhw0ubj48OwYcP43//SLpk5efIk/v7+WRaYlI38u8PumXAvBEL+VSa6lt6OG8cgIUaZF7ZwRbWjkd42m4LK+G3L2sORn5V1ZrbQ4hslqcvjUuYXbVjahQZyflFJyrQM/8ypUqUKx44dS/dxjUaDeJPJkaW3x8IOqvZUlvfNUTWUfCdl6qri9cHIWNVQJJWUaAzV+z+7/85kZbq5PG77hTvsvHgXU2MN41qWUTscScqVMlzSNmvWLBIS0m+4XqFCBXQ6XZYEJb0F1T+CA98ro/JfPQjFaqgdUf6gH+pDjs+WrzWeCI9vg7kdVO6udjTZLiFZy+SnQ3z0rO2Fl7O1yhFJUu6U4ZI2V1dXPDw8sjMW6W2ydYUKnZTlvXNUDSXfePJAqR4F8G6gbiySukwt4P3F0PrbfNGmdNG+cMLvx1HQ1pxBDeX8opL0ujL8afHrr7++tKRNyoVqDgY0ELIZIi+oHU3ed3mXMhaXcymwl1P2SPlDZEw83227BMDopqWxMc9wBY8kSS/IcNLWp08foqOj9fcLFy5MeHh4dsQkvS3OPuDbUlmWE8lnPzl1lZQPffXvBWITtVR0d6Bdpbzfdk+SslOGk7YXOxk8evRItmHLC2oNVf6eXgnRN1QNJU8TAsJ2KMtyqA8pnzgW8YC/jyufK5Pk/KKS9MZyRGOK77//Hk9PTywsLKhevTqHDx9+6fYPHz5k4MCBuLm5YW5uTsmSJdm0aZP+8fnz5+Pn54ednR12dnYEBASwefNm/eNRUVEMGjSIUqVKYWlpSbFixRg8eLBBSWK+UbQKeNRWxoo6+IPa0eRd90Mh+ioYm4FHTbWjkaRsp9MJJq1XZn54378oFdwd1A1IkvKADCdtGo0GzXPjCL14/3WtWLGC4cOHM2HCBI4fP06FChUIDAwkMjIyze0TExNp0qQJ4eHhrFq1iosXL/LTTz9RpMizYveiRYvy1VdfcezYMY4ePUrDhg1p06YN584pHyA3b97k5s2bzJw5k7Nnz7J48WL+/fdfevXq9cbPJ1dKmUj+2GJ48lDNSPKulKrRYgFgJnvOSXnfqmPXOX09GltzEz5tWlrtcCQpT9CIDA6uZmRkhL29vT5Re/jwIXZ2dhi90PMpKioqUwFUr16dqlWrMm/ePAB0Oh3u7u4MGjSI0aNHp9p+wYIFzJgxgwsXLmBqaprh8zg5OTFjxox0E7O//vqL//3vf8TGxmJi8uqGsjExMdjb2xMdHZ3uLBG5hhAwv5Yy/2Gj8VBnhNoR5T3LOsClIGg8CWoPVTsaScpWMfFJNJy5k3uPExnb3Jc+dYurHZIk6eXm7+8Md+NZtGhRlp88MTGRY8eOMWbMGP06IyMjGjduzIEDB9LcZ926dQQEBDBw4ED++ecfChYsSOfOnRk1ahTGxqkHK9Vqtfz111/ExsYSEBCQbiwp/7yMJGx5jkajlLat6QsHF0CNgcqQBFLWSE6A8D3KsmzPJuUD3/53iXuPEyle0JpuNT3VDkeS8owMZyjdunXL8pPfu3cPrVZLoUKFDNYXKlSICxfSHoLi8uXLbN++nS5durBp0yZCQ0MZMGAASUlJTJgwQb/dmTNnCAgIID4+HhsbG9asWUOZMmmPwn3v3j2mTJlC37590401ISHBYMiTmJiYzDzVnK/cu7B9CkRfg1N/QJUeakeUd1w7DElxYO0ChcqpHY0kZavQyEcs3h8OwHg5v6gkZalc927S6XS4uLjw448/4u/vT8eOHRk7diwLFiww2K5UqVKcPHmSQ4cO0b9/f7p160ZwcHCq48XExNCiRQvKlCnDxIkT0z3vtGnTsLe319/c3d2z+qmpy9gUAgYqy/u/BZ1W3XjykpSpq7wb5IuBVKX8SwjBpPXBJOsEjX1dqF9Kzi8qSVlJ1W8QZ2dnjI2NuXPnjsH6O3fu4OrqmuY+bm5ulCxZ0qAq1NfXl9u3b5OYmKhfZ2Zmho+PD/7+/kybNo0KFSowd+5cg2M9evSIpk2bYmtry5o1a17aRm7MmDFER0frb9euXXudp5yzVfoQLBwg6jJc2KB2NHmHnLpKyif+Ox/Jnkv3MDM24vMWcn5RScpqqiZtZmZm+Pv7s23bNv06nU7Htm3b0m1/VqtWLUJDQw3GiAsJCcHNzQ0zM7N0z6XT6VJVb77zzjuYmZmxbt06LCxe3obL3NxcP4RIyi3PMbeBak+riPfOUTooSG8m9h7cOqUsF6+vaiiSlJ3ik7RM2aDUZvSq44WnnF9UkrKc6nU1w4cP56effmLJkiWcP3+e/v37ExsbS48eSpuqrl27GnRU6N+/P1FRUQwZMoSQkBA2btzI1KlTGThwoH6bMWPGsHv3bsLDwzlz5gxjxoxh586ddOnSBXiWsMXGxvLLL78QExPD7du3uX37NlptPq8WrNYXTCzg5nEI36t2NLlfyoC6hcqDbaGXbytJudgve69wNSoOF1tzBjbwUTscScqTXrurZGJiIleuXMHb2/uNelx27NiRu3fvMn78eG7fvk3FihX5999/9Z0Trl69ajCsiLu7O0FBQQwbNgw/Pz+KFCnCkCFDGDVqlH6byMhIunbtyq1bt7C3t8fPz4+goCCaNGkCwPHjxzl06BAAPj6GHy5XrlzB09PztZ9PrmdTECr9D478DPvmgFcdtSPK3fRTV8leo1LedTs6nu93hAIwprmcX1SSskuGx2lLERcXx6BBg1iyZAmgVE0WL16cQYMGUaRIkTTHVsuLcvM4L68UdQW+q6xMbv7RPnCVPR5fixAwqzQ8vg1d/5HVo1KeNfTPE6w9eZPKxRxY3b9mlgy8LknZJTd/f2e6enTMmDGcOnWKnTt3GrQDa9y4MStWrMjS4CSVOHlBmbbK8r65L91UeonIYCVhM7EE9xpqRyNJ2eJoeBRrT95Eo4GJrcvKhE2SslGmk7a1a9cyb948ateubfDmLFu2LGFhYVkanKSilKmtzq6Gh1fVjSW3Sqka9awtByuW8iStTjDx6fyiHfzd8SvqoG5AkpTHZTppu3v3Li4uqcfeiY2Nlb+w8pLCFZXqPKGFA9+rHU3upB/qQ7Znk/KmxfvDOXsjBlsLE0Y2LaV2OJKU52U6aatSpQobN27U309J1H7++eeXThMl5UK1hip/j/8GcZmbUzbfS3oCEfuVZZm0SXnQ9QdxzNpyEYAxzXxxtjFXOSJJyvsy3cVn6tSpNGvWjODgYJKTk5k7dy7BwcHs37+fXbt2ZUeMklqK1wdXP7h9Gg7/BPVHvXIX6amI/ZAcD3ZFoKAsgZDyFiEEn689S1yilmqeTnxQNY/NECNJOVSmS9pq167NqVOnSE5Opnz58mzZsgUXFxcOHDiAv79/dsQoqUWjgdpDleXDCyExTtVwchV91WgD5TpKUh6y4fQtdl68i5mxEVPfLY+RkXyNS9LbkKmStqSkJPr168e4ceP46aefsismKSfxbQMOHvAwAk4ug2p91I4od5BTV0l51MO4RCY97XwwsIEPPi42KkckSflHpkraTE1NWb16dXbFIuVExiZQc5CyvP9b0CarG09uEHNLGe4DjRybTcpzpm26wL3Hifi42PBR/eJqhyNJ+Uqmq0fbtm3L2rVrsyEUKceq2AWsCihDfwSvVTuanC+llK1wJbByUjcWScpCB8Lus+LoNQCmvVsecxNjlSOSpPwl0x0RSpQoweTJk9m3bx/+/v5YWxtOCjx48OAsC07KIcysoPpHsONLZWqrcu1lO62X0U9dJatGpbwjPknLZ2vOANClejGqesofJJL0tmV6GisvL6/0D6bRcPny5TcOKjfIzdNgvJa4KJhdFpLi4MM1chiL9Oh0MNMH4u5Dj83gUVPtiCQpS8zacpHvtofiYmvOfyPqYWdhqnZIkvRacvP3d6ZL2q5cuZIdcUjPeZKoxdIsh1U7WDlB5W5waD7snSOTtvTcPqUkbGa2ULSq2tFIUpa4ePsR83cqM95MblNWJmySpJJMt2l7nhCCTBbUSa+w7fwd6kzfzpnr0WqHklrAANAYw5VdcPOE2tHkTClVo151wVh+sUm5n04nGP33aZJ1giZlChFY1lXtkCQp33qtpO23336jfPnyWFpaYmlpiZ+fH7///ntWx5bvCCH4/WAE9x4n0nPJEa4/yGHjojkUg/LvKctyIvm0he1Q/no3UDcOScoiSw9FcOLqQ2zMTZjcRk4IL0lqynTS9s0339C/f3+aN2/OypUrWblyJU2bNuWjjz5i9uzZ2RFjvqHRaPi2UyVKu9py91ECPRcfIfpJktphGar5tKNJ8D8QlT/aL2ZYwmO4elBZltXHUh5wK/oJ0/9Vpqr6tGkp3OwtVY5IkvK3TCdt3333HfPnz+frr7+mdevWtG7dmunTp/PDDz/w7bffZkeM+YqdhSm/dq+Ki605IXce03/pMRKTdWqH9YxrOfBpAkIH++epHU3OEr4XdEng6AkFvNWOJkcTQqDTCbQ6QbJWR5JWR2KyjoRkLfFJyu1Jopa4xGRiE5J5nJDMo/gkYuKTeBSfw37I5GET/jnH44RkKhVzoEt1D7XDkaR8L9MdEW7dukXNmql7xNWsWZNbt25lSVD5XWEHS37tXpUOCw+wP+w+n605w4z3/HJOtUTtoRC6VZkhof4YsCmodkQ5Q9g25W8OLGW7/ziBfr8f43ZMPCnNUIUQCEAIEIinf3n6+PP3n9vu6TJpPfbcPqQ6pnju2G+uYWkXFvzPHzOTN2qWK73Ev2dvsSX4DiZGGr561w9jOVWVJKku0594Pj4+rFy5MtX6FStWUKJEiSwJSoJyRez5vnNljDSw6th1vtseqnZIz3jUgiL+yoTohxeqHU3OkYOnrlp+6CpHIx5w/cETbjxUbjej47kVHc/tmHjuxCQQ+SiBu48SuPc4gXuPE7kfm0hUbCIP4pJ4GJdE9JMkYuKTeRSfzKOnpV+xiVriErU8SdISn6QjIVkpMUvU6kjSCpKflqbpRNYlbADbL0TyxcbgrDugZCAmPonx/yhTVX1Uz5tSrrYqRyRJErxGSdukSZPo2LEju3fvplatWgDs27ePbdu2pZnMSa+vQWkXJrcpx+drz/LN1hDcnSxpV6mo2mEpA+vWGgorP4TDPynL5vl8/sEHEXA/VOld61VH7WgMCCFYdfw6ACMDS1HTuwAajQYNyr9Sg0Y/VvLz9w2WSRlP+fn7yjGMNC/sn8axn+76wvEMt0N/zrSPkXL8vZfu0WvJUX47EEFFdwferZwD3hN5zNebLxD5KAEvZ2s+buijdjiSJD2V6aStffv2HDp0iNmzZ+uns/L19eXw4cNUqlQpq+PL9/5Xw4NrUXEs3H2ZT1edxtXOkgDvAmqHBaVbgJM3RIXB8d+U4UDys5RStqJVwcJe3VhecCT8ARH347AxN6FHLU+szDL9ts9RGvkWYkijEszddonP1pyhtKsdZQrnrgEyc7Ij4VEsO3QVgKntymNhmsPGjJSkfOy1GoT4+/uzdOlSjh07xrFjx1i6dKlM2LLRqKalaV7elSStoN/vRwmNfKR2SGBkDLWe9iQ98D1o83nj8Bw8ddVfT+eKbFHeLdcnbCmGNCpB/VIFiU/S8dHSY0TH5fPXXxZJSNYy5m9lqqoOVYrmjB+IkiTpZTpp27RpE0FBQanWBwUFsXnz5iwJSjJkZKThmw4VqVzMgZj4ZLovOsLdRwlqhwV+H4C1C8Rch7Or1Y5GPdpkZcBhyHGdEGITktl4Rukg9F6VvFONaGSkYU7HihR1tORqVBzDV55Ep5MDfb+pBTsvExr5GGcbMz5r7qt2OJIkvSDTSdvo0aPRarWp1gshGD16dJYEJaVmYWrMT12r4FHAiusPntB7yRGeJKb+P7xVphZQo7+yvG9u1rY0z01uHof4aLBwgMI5q8R589nbxCVq8SxgRRUPR7XDyVIOVmYs+J8/5iZGbLsQyfc7clBnnVwoNPKx/hqOb1UWByszlSOSJOlFmU7aLl26RJkyZVKtL126NKGh/2/vzsNjOtsHjn9nsu8RkY3EHgkSJGKtotZStbW8fbWW6oYWpQtV2qqli2p1o9W++OlCtdROVe3VIBIJidjFkggiu2wz5/fHNFOpLSGZM5Pcn+uaizlz5jn3zEky9zzPee5H/mhWpOrOdiwaHoG7ow2Hzmcwblk0OrV7F1o+bVhnMzUejm9RNxa1FA+N1utkGDY2I8VDo4+F1zKfkjHlqGlNN2b0awrA3N+PsePYZZUjskx6vcIbK+Mo0Onp1KgGfUJ91Q5JCHELZU7a3NzcOHXq5kr4J06cwMnJqVyCErdXr4YzC4e2xNZKy2/xl5i5PkHdgBzcoeVww//3fKJiICoylvowr6HRpKu5RJ5OQ6OhUs+wfLylP/9tHYCiwLhl0ZxLM7Pl3yzA8gPn2HcmDQcbK2b0a1opE3whKoMyJ219+/Zl/PjxnDx50rjtxIkTTJw4kUcffbRcgxO3FlHHgzmDmgHwvz2nWbTntLoBtRkNWhs4uwfO7Vc3FlO7ng7nDxj+b2ZJW3GZjwcaeOLnXrmXH3qrT2Oa+buTnlvIqO+jyCtU+dIBC5KamcesDYYvfxO7B1KrmqPKEQkhbqfMSdsHH3yAk5MTQUFB1K1bl7p16xIcHEz16tWZM2dORcQobuHRZn681rMRANPXxfPbkRT1gnH1g9DBhv9Xtd620ztB0YFnILj7qx2NkV6v8EuUIWl7LLzy9rIVs7O2Yv6QMDycbDl8IZNpqw+jVNVrLMvonbXxZOUVEVrLjRHt66odjhDiDu5pePTPP/9k/fr1jB49mokTJ7J161b++OMP3N3dKyBEcTujOtbniVb+KAqMXRbNoXPp6gVTXP7j6Hq4cly9OEzNTJeu2nvqKhfSr+Nib02PJj5qh2MSfu4OfPZEC7Qa+OnAeZbtP6d2SGbv9/hLrI9LxkqrYVb/EFmqSggzd0912jQaDd27d+fVV1/lxRdf5MEHHyzvuEQpaDQapvdtyoOBhnpVI5ccUO96nhqNoFEvQIE/P1UnBlNTFDhhnktX/fx3L9ujzfyqVHHU9g08ebVHEGBY7FzVLzJmLju/iGmrDwPwzAN1aVrTvIpCCyFuVuqkbe/evaxbt67Etv/7v/+jbt26eHl58dxzz5Gfbwa1w6oYGystX/y3BUE+LlzJzmfE4v1kXFep0Gj78YZ/Dy2DLBWHa00l7RRkJBmu56vTXu1ojDLzCtl4+O/abFVgaPTfXuhYjx5NvCnQ6Rn1XRRpOQVqh2SW5mxO5GJGHv4eDozvGqh2OEKIUih10jZ9+nSOHDlivB8XF8fIkSPp2rUrkyZNYu3atcyePbtCghR35mJvw6IREXi72nEiNZsXlkZRUKQ3fSABrcG/DegK4K/5pj++qZ34e2g0oA3Yms/M6Q2xyeQV6mng5Uxzf3e1wzE5jUbDh483o56nExcz8hj7oxmUxjEzMefSWbL3DAAz+4XgYFt1emOFsGSlTtpiYmLo0uWfIaBly5bRunVrFi5cyIQJE/j0009lwXgV+bo58L/hETjZWrH31FUmrYxV50LsB8Yb/j3wP8jLNP3xTclMl65accMEhKpausHV3oYFT4XjYGPF7hNXmLslUe2QzEahTs+kX2JRFOjfoiYPBtZQOyQhRCmVOmm7du0a3t7exvs7duzg4YcfNt6PiIjg3Dm58FdNTfzc+HxIGFZaDSsPXmDeVhUmBDTsATWCID8TohaZ/vimUlQAZ3YZ/m9GkxBOXs4m6uw1rLQaBrSoqXY4qgr0duH9x0IB+GLbSXVnWJuRhbtOcTQli2qONrzZW5aqEsKSlDpp8/b25vRpQz2wgoICDh48SJs2bYyPZ2VlYWNjU/4RijLp3MiLd/saKsR/8vtx4wXpJqPVQrviheS/hKJKep3j+X1QkA2OnuAdonY0RsVlPjoG1sDL1V7laNT3aDM/nv67jMXEnw5x+kqOyhGp68yVHOb9bvgy92bvxlR3tlM5IiFEWZQ6aevVqxeTJk1i165dTJ48GUdHRzp06GB8PDY2lvr161dIkKJs/ts6gBc6Gs7FpF9i+fPEFdMGEPI4uPhBdgqsHQ9XKuHyZjeugqC9p0nY5U6nV1h58AJQNScg3M7kXkFE1KlGVn4RLyyNIregSO2QVKEoClN+jSO/SM8DDTwZEFa1e2KFsESl/rR59913sba2pmPHjixcuJCFCxdia/vPgsL/+9//6N69e4UEKcrutR6N6B3qS5Fe4fnvojh+Kct0B7e2hQdfMfz/0A/weTj8Xz9IWAe6SvKBaYZLV+06fpmUzDzcHW3oEuyldjhmwzDDOowaLnYkXspi8sq4Kll495eDF9hz4ip21lpm9pelqoSwRNal3dHT05OdO3eSkZGBs7MzVlYlZxutWLECZ2fncg9Q3ButVsNHjzcjJSOPqLPXGL5oP6vGtMPLxURDZhEjoVpt2LcQjm2GU9sMN9dahrVKw4aBs4UmFjlX4WKM4f/1O6sayo2Kh8L7Na+JnbXMBryRl6s9Xw4J44mv/2J1zEWa+7tXqer/V7LzmbE+HoDxXQOpXd18ZjsLIUrvnlZE+HfCBuDh4VGi502oz97GioVDW1KnuiMX0q/zzJIDph0aatAV/rscxh0y1HBzrA6Z5+GPGTC3Mfw8EpL+MhSptSSntgEKeDcFF/NYbSAjt5Df4i8BMjR6OxF1PJjy94X3M9cnsP9MmsoRmc6MdfGk5xYS7OvKMx2qTrIqRGVjHhfjiArj4WTLohGtqOZoQ+z5DMb+GGP6mlXVakO3d+DleOj/NdSKAH0hHP4Z/tcDFjxgKBGSn23auO6VcWjUfHrZ1sRepKBIT5CPC038XNUOx2wNb1eHR5v5UaRXGP39QVIz89QOqcLtOHaZX2MuotXAewNCsLGSP/tCWCr57a0C6no6sXBoS2yttfyecIl318WrE4iNPTQbDM/8Ds/tgBZPgbUDXDoM616GucGw4TW4fEyd+EpDUW5I2synPtvPBwzldqpybbbS0Gg0vDcwhEbeLlzOyufFH6Ip1KlQiNpEcguKmLIqDoDh7erSrAoWWxaiMpGkrYpoWceDuYOaAbD4zzP8b/dpdQPyaw59P4eJCdBjFnjUM9R22/cVfBEBS/pA/Grzm7hw+ShkJYO1PQS0VTsaAI5dyuLQ+QystRr6V/HabKXhaGvN/CfDcLGzZt+ZNN7beFTtkCrMJ78f5/y169R0d2Bid1mqSghLJ0lbFfJIqB+THjYspv3u+ng2m0OxUYdq0HYMvBgFT66ERr1Bo4XTO+GnofBJCGx/33zWMi1euqp2e0PPoRkonoDwUJCX1N0qpXo1nPno7y8x3+4+zdpDF1WOqPwdvpDBN7tOAfBuvyY42ZV63pkQwkxJ0lbFPP9gPf7bOgBFgXHLook5l652SAZarWE5qCd+gHGx0GGioXBt1kXYPgs+bgIrhsOZPepOXDCzpasKdXqpzXaPujfxYXQnQz3D13+J5Zgpy+JUsCKdnkkrY9Er8EioLw8Fed/9SUIIsydJWxWj0WiY/mgTOgbWIK9QzzNL9nMuLVftsEpy94cu02BCPAz81rAIvb4IjqyCxb1gfjvY/w3km/hDtjAPzu4x/N9M6rPtSLzMlex8PJ1t6RxkoSVUVDSxeyMeaOBJboGOF5ZGkZVXqHZI5WLxn2c4fCETV3trpvVprHY4QohyIklbFWRtpeWLIWEE+7pyJbuA4Yv2kZFrhh9W1nYQ8hiM3Awv7Ibw4WDjCKnxsH4ifBRk+Dc1wTTxJP0JRXmG1R5qBJnmmHdxY202mRVYdlZaDfP+0xw/N3tOXcnhlRWHLL7w7rm0XD76zTCZZ0rvYNPVZhRCVDj5K19FOdtZs2h4BD6u9py8nMPz3x2goMiMZ9H5hECfeTDxKPR8H6o3NKz9uf8b+LINLOpt6InTVWDyeeMqCGYwQzMtp4CtR/+uzdZShkbvVXVnO758MhxbKy2bj1ziq52n1A7pnimKwpu/HuZ6oY7WdT0Y1NJf7ZCEEOVIkrYqzMfNnkUjInC2s+avU2lM+iXW/HsZ7N2gzQvw4n4YuhqC+4DGCs7uNlzz9nFT2DYLMivgwvKT2wz/mkl9ttUxFyjUKYTUdCPIR2qz3Y/m/u68/WgTAD7YdJQ9pl6vt5ysOXSRHccuY2utZdaAECn/IkQlI0lbFRfs68oXQ8Kw0mpYGX2BT34/rnZIpaPRQL1OMPg7GB8HD74GTl6GRep3vG9I3pY/ZZiFWh6JaFaKoZ4cGqhnHknbigOGoVGZgFA+nmjlz6CWtdAr8NKP0VxMv652SGVyLaeA6WsNNRhf6tyA+jVkWUEhKhtJ2gQdA2swo19TAOZtPW68TspiuNWEh6bAy0fgsf8ZynEoOkhYY6j39kVriPwa8jLu/RjFvWx+zcGpermEfT+OXMwgPjkTWystfZv7qR1OpaDRaJjetylNa7qSllPAqO8Pkl+kUzusUpu1IYGrOQUEejvzfMf6aocjhKgAqidtX3zxBXXq1MHe3p7WrVuzb9++O+6fnp7OmDFj8PX1xc7OjsDAQDZs2GB8fP78+YSGhuLq6oqrqytt27Zl48aNJdr4+uuv6dSpE66urmg0GtLT0yvipVmUJ1oFGMsfTPol1jKHh6xtoelAGLEBRu2FliPB1hmuJMLGV+GjYFg7Hi4dKXvbJ/+uz2Yms0aLE+tujb1xd5Q1f8uLvY0V84eE4+5ow6Fz6caeK3P354krrIg6j0YDsweEYmut+p92IUQFUPU3e/ny5UyYMIG33nqLgwcP0qxZM3r06EFqauot9y8oKKBbt26cOXOGn3/+mcTERBYuXEjNmv9Uga9VqxbvvfceUVFRHDhwgIceeoi+ffty5Mg/H9S5ubn07NmTN954o8JfoyV5pXsj+vy9LuML30VZdt0q78bwyFyYkAC95hhmexbmQNQiQ8mQ//WEuJ+hqODuben1N1zPpn59toIiPatjDNfsydBo+fP3cOSTwc3RaOD7yCRW/L1EmLnKK9Txxt9LVT3ZujbhtaupHJEQoqJoFBWvPG/dujURERF8/vnnAOj1evz9/XnppZeYNGnSTfsvWLCADz/8kKNHj2JjY1Pq43h4ePDhhx8ycuTIEtu3b99O586duXbtGu7u7mWKPTMzEzc3NzIyMnB1rTwXgecV6njq20j2n7lGTXcHVo1pVzlKBigKnNltmG16dJ2h7huAUw0IGwYtR4DbbRKg5EPw1YOGXrvXTht69FS06XAyL3x3EC8XO/6c9BDWUuqjQny69ThztxzDzlrLL6Pa0bSmm9oh3dKHm4/yxbaTeLvasWVCR1ztS/+3UYiqyJI/v1X7a19QUEBUVBRdu3b9Jxitlq5du7J3795bPmfNmjW0bduWMWPG4O3tTdOmTZk1axY63a2vO9HpdCxbtoycnBzatjWPdSLNnb2NFV8/1ZK6nk5cSL/OyMUHyC0ws/U/74VGA3U7wKAlMP4wdJoMLr6Qcxl2zTEsl7VsiKFH7d/fY4qXrqrTQfWEDf4ZGh0QVksStgr0YucGPBTkRX6RnlHfR5GeW4peWRNLSM7kqx2GEiXT+zaVhE2ISk61v/hXrlxBp9Ph7V1yeRVvb29SUm69zuSpU6f4+eef0el0bNiwgalTp/LRRx8xY8aMEvvFxcXh7OyMnZ0dL7zwAqtWraJx4/urCp6fn09mZmaJW2VVzcmWRcMj8HCyJe5CBmN/jEanN/NSIGXh6gudJhlmnT6+xJCMKXpDD9zSfvB5S/hrPlxPN+xvRktXpWblsS3xMiBDoxVNq9Xw8aDmBHg4ci7tOuOXx6A3o98DnV5h8so4ivQKPZp406OJj9ohCSEqmEV9Tdfr9Xh5efH1118THh7O4MGDmTJlCgsWLCixX6NGjYiJiSEyMpJRo0YxbNgw4uPv74Li2bNn4+bmZrz5+1fuopV1PJ1YOLQlttZafk9IZfraI+Zfw62srGygST8Yvg5GR0Kr58DWBa6egE2TYG4wrH4Rkv4y7G8GkxBWR19Ep1doEeBOAy8p6VDR3BxtWPBkOHbWWrYnXubTP8ynJM7SvWeIOZeOi5017zzaVO1whBAmoFrS5unpiZWVFZcuXSqx/dKlS/j43Pobo6+vL4GBgVhZWRm3BQcHk5KSQkHBP0MXtra2NGjQgPDwcGbPnk2zZs2YN2/efcU7efJkMjIyjLdz58z74uTyEF67Gp8Mbg7Akr1n+d+eM6rGU6G8gqDXh4YVF3rPBa/GUJgL0UtBXwjuAeBRT9UQFUVhRZTh50562UynsZ8rs/qHAIaSONuO3nqilCldTL/Oh5sTAXjt4SB83CrBdadCiLtSLWmztbUlPDycrVu3Grfp9Xq2bt162+vP2rdvz4kTJ9Dr/1lu6dixY/j6+mJre/trjfR6Pfn5+fcVr52dnbGMSPGtKugV4ssbvQzrbM5YH8+mw7ceuq407JwhYiSM+hNGbDSUELF1MfTCqVxdPvZ8BscuZWNnraVPM6nNZkoDw2vxVJvaKAqMWxZN0tVc1WJRFIVpqw+TU6AjvHY1hrQKUC0WIYRpqTo8OmHCBBYuXMiSJUtISEhg1KhR5OTkMGLECACGDh3K5MmTjfuPGjWKtLQ0xo0bx7Fjx1i/fj2zZs1izJgxxn0mT57Mzp07OXPmDHFxcUyePJnt27czZMgQ4z4pKSnExMRw4sQJwHANXExMDGlpaSZ65Zbl2Q71eLJNAIoC45dHE510Te2QKp5GA7XbGYr1vnEe2r2kdkTGCQg9m/rIBecqmPpIY1oEuJOZV8Tz30VxvUCdwrsbD6fwe0IqNlYaZg8IQauVpaqEqCpUTdoGDx7MnDlzmDZtGs2bNycmJoZNmzYZJyckJSWRnJxs3N/f35/Nmzezf/9+QkNDGTt2LOPGjStRHiQ1NZWhQ4fSqFEjunTpwv79+9m8eTPdunUz7rNgwQJatGjBs88+C8CDDz5IixYtWLNmjYleuWXRaDS83acJnRvVIK9QzzNLDqja01AV5RXqWB1zAZChUbXYWmv5ckgYns62JCRnMuXXOJNf55lxvZC31hhqTo7qWJ9AbxeTHl8IoS5V67RZMkuu83KvcvKLGPTVXo5czKReDSdWjmon1fhNZO2hi7z0YzR+bvbsev0hrKR3RTV7T17lyW8j0ekVZvRrypNtapvs2JNXxvHjviTq1XBiw9gO2NtY3f1JQogSLPnz26Jmjwp1OdlZ87/hEfi62XPqcg7PL42yqLUZLVnx0OjA8FqSsKmsbf3qvN6zEQDvrD1isssF9p1O48d9SQDM6h8iCZsQVZAkbaJMvF3tWTQiAmc7ayJPp/H6z7GVrxSImUnJyGPXcUNttoFhMjRqDp7tUI+Hm/pQqFMY/f1BrmTf30Snu8kv0jF5ZSwA/4nwp0296hV6PCGEeZKkTZRZkI8r858Mw1qr4deYi3y85ZjaIVVqK6PPo1egVR0P6ng6qR2OwHCd54ePN6N+DSeSM/J46YdoinT6uz/xHn257SQnL+fg6WzH5IeDK+w4QgjzJkmbuCcdGtZgZn9DQc9P/zjBT2a+qLalUhSFnw8YhkZlAoJ5cbaz5qunwnGytWLvqavM+a1ivrycSM3iy+2Gme5vP9oYN0eZOSxEVSVJm7hngyMCeLFzAwDeWBnH7uNXVI6o8jmYdI1TV3JwsLGiV6iv2uGIf2ng5cIHjzUDYMGOk2w6nHyXZ5SNXq8w6Zc4CnUKDwV50TtEfgaEqMokaRP3ZWL3QPo296NIrzDquygSU7LUDqlSKZ6A0CvEF2c7a5WjEbfSO9SXZzvUBeCVFbGcvJxdbm3/uD+JA2ev4Whrxbv9mqJRucCzEEJdkrSJ+6LRaPjgsVBa1fEgK7+IEYv2cSkzT+2wKoXrBTrWHjL03MjQqHl7vWcQret6kJ1fxAtLo8jJL7rvNi9l5vHehqMAvNK9ETXdHe67TSGEZZOkTdw3O2srvh4aTr0aTlzMyGPkkv3l8qFV1W06kkx2fhH+Hg60ruuhdjjiDqyttHz+3zC8Xe04nprNa7/c/6zqt9ccISu/iGa13BjWrk75BCqEsGiStIly4e5oy+LhrajuZMvhC5m89GPFzqarCoqHRh8L85eliixADRc7vhwSho2VhvWxyXy7+/Q9t/XbkRQ2Hk7BSqth9oBQqc0nhAAkaRPlKKC6IwuHtcTOWssfR1N5Z2281HC7R+ev5fLnyasADAirqXI0orTCa3sw9ZHGAMzeeJTIU1fL3EZWXiHTVhuWqnq2Qz0a+1lWxXYhRMWRpE2Uq7CAanwyuDkaDSz96yzfRSapHZJFWnnwAooC7epXx9/DUe1wRBk81aY2/VvURKdXGPNDdJmv8ZyzOZGUzDxqV3dkfNeGFRSlEMISSdImyt3DIb5M6hkEwOwNCVxIv65yRJZFr1f+GRqVCQgWR6PRMKt/CEE+LlzJzmf09wcpKCrdpQIHk67xf3+dBWBmP1mqSghRkiRtokI826EeEXWqkVugY+qvh2WYtAz2nUkjKS0XZztrHm4qdbkskYOtFQueDMfF3pqos9eYtSHhrs8pKNIz+Zc4FMWwXNkDDT1NEKkQwpJI0iYqhFarYfaAEGysNPxxNJX1ceVbdLQyK+5leyTUFwdb6WmxVHU8nfh4UHMAFv95htUxF+64/8Jdp0i8lIWHky1TestSVUKIm0nSJipMAy8XRncyrJjw9pp4MnILVY7I/OXkF7EhTmqzVRZdG3vz0kOG34FJv8RxNCXzlvudupzNvK3HAZj6SDAeTrYmi1EIYTkkaRMVanTn+jTwcuZKdn6phoiquvVxyeQW6Kjr6UR47WpqhyPKwfiugXRo6Mn1Qh0vLI0iM6/klxdFUXhjVRwFRXo6NPSkX3OZLSyEuDVJ2kSFsrO2YvaAEACWHzjH3pNlL4FQldw4AUGWLKocrLQaPv1PC2q6O3Dmai4Tlh9Cr//nGs8VUef561Qa9jZaZvYLkfMuhLgtSdpEhYuo48F/WwcAMGVVHHmFOpUjMk9nr+aw73QaWo3UZqtsqjnZMv/JMGyttfyecIn5O04CcDkrn5nrDT3QE7oFElBdyrsIIW5PkjZhEpMeDsLLxY5TV3L4YtsJtcMxS7/83cv2QMMa+LrJOpOVTWgtd97t2wSAOb8lsvPYZd5dF0/G9UIa+7rydPu6KkcohDB3krQJk3C1t2H63x9Y87efJDElS+WIzIter/DLQcPsQpmAUHkNjgjgPxH+KAqM+i6KNYcuotXA+wNDsbaSP8dCiDuTvxLCZHo08aFbY2+K9AqTVsai00vttmJ/nrzKhfTruNpb072xt9rhiAr09qNNCK3lRk6B4TKBp9vXJaSWm8pRCSEsgSRtwmQ0Gg3T+zbB2c6a6KR0vo88q3ZIZuPnqHMAPNrcT6rgV3L2NlZ8OSQMb1c7Gng583K3QLVDEkJYCEnahEn5ujnwWs9GAHywKZHkDFniKjOvkI2HUwB4LNxf5WiEKdSq5siOVzuzYWwHnOys1Q5HCGEhJGkTJvdk69qEBbiTnV/E1F+PVPklrtYdSia/SE9DL2eayTBZlWFvY4WttfwJFkKUnvzFECan1Wp4b2AoNlYafk+4xKa/e5mqquKhUanNJoQQ4k4kaROqCPR24YWO9QGYtuYIGder5hJXJ1KzOZiUjpVWQ3+pzSaEEOIOJGkTqhnTuQH1PJ24nJXP+5uOqh2OKn45aKjN1imwBl4u9ipHI4QQwpxJ0iZUY29jxay/l7j6ITKJfafTVI7ItHR6hZUH/1m2SgghhLgTSdqEqtrUq85/IgwzJievjCW/qOoscbXz+GUuZeZTzdGGLsFSm00IIcSdSdImVDf54WA8ne04eTmHL7edVDsckyleHL5v85oyi1AIIcRdySeFUJ2bow1vP9oYgC+3n+D4pcq/xFV6bgFbjlwCZGhUCCFE6UjSJsxC7xBfugR5UahTmLwyDn0lX+JqzaGLFOj0BPu60rSm1GYTQghxd5K0CbOg0Wh4t19TnGytOHD2Gj/sS1I7pApVPDQqvWxCCCFKS5I2YTb83B14pYdhiav3Nx4lJSNP5YgqRmJKFrHnM7DWaujX3E/tcIQQQlgISdqEWRnatg7N/N3Jyi/i7TVH1A6nQhSvgNAl2IvqznYqRyOEEMJSSNImzIqVVsN7A0Kw1mrYdCSFzUcq1xJXhTo9q6IvALI4vBBCiLKRpE2YnWBfV557sB4A01YfJiuv8ixxtT3xMleyC/B0tqVToxpqhyOEEMKCSNImzNLYLg2pU92RS5n5fLApUe1wyk3x0Gj/FjWxsZJfPyGEEKUnnxrCLNnbWDGrv2GJq+8izxJ11vKXuLqanc/WhFRAhkaFEEKUnSRtwmy1a+DJ4+G1UBSY9EscBUV6tUO6L7/GXKRIrxBay41GPi5qhyOEEMLCSNImzNqU3sFUd7LleGo2C3ZY9hJXUptNCCHE/ZCkTZg1d0dbpvUxLHH1+R8nOHk5W+WI7s3hCxkkJGdia6Xl0WZSm00IIUTZSdImzN6jzfzo1KgGBTq9xS5xVdzL1q2JN+6OtipHI4QQwhJJ0ibMnkaj4d2+TXGwsWLf6TSWHzindkhlUlCkZ3VMcW02GRoVQghxbyRpExbB38ORid0DAZi1IYHUTMtZ4mprwiWu5Rbi7WrHgw2lNpsQQoh7I0mbsBgj2tcltJYbWXlFvLM2Xu1wSq14aHRAWC2stBqVoxFCCGGpJGkTFsNKq2H2gBCstBrWxyXze/wltUO6q9SsPLYfuwzI0KgQQoj7I0mbsChN/Nx4pkNdAKauPkx2fpHKEd3ZqoMX0OkVwgLcqV/DWe1whBBCWDBJ2oTFGd8lkAAPR5Iz8piz2XyXuFIU5YbabLICghBCiPtjFknbF198QZ06dbC3t6d169bs27fvjvunp6czZswYfH19sbOzIzAwkA0bNhgfnz9/PqGhobi6uuLq6krbtm3ZuHFjiTby8vIYM2YM1atXx9nZmYEDB3LpkvkPtwlwsLViZv+mACzZe4bopGsqR3Rrh85ncDw1G3sbLY8081U7HCGEEBZO9aRt+fLlTJgwgbfeeouDBw/SrFkzevToQWpq6i33LygooFu3bpw5c4aff/6ZxMREFi5cSM2aNY371KpVi/fee4+oqCgOHDjAQw89RN++fTly5Ihxn5dffpm1a9eyYsUKduzYwcWLFxkwYECFv15RPjo0rMGAsJooCkxeGUehzvyWuCpeHL5nEx9c7W1UjkYIIYSl0yiKomql0tatWxMREcHnn38OgF6vx9/fn5deeolJkybdtP+CBQv48MMPOXr0KDY2pf8g9PDw4MMPP2TkyJFkZGRQo0YNfvjhBx577DEAjh49SnBwMHv37qVNmzZ3bS8zMxM3NzcyMjJwdXUtdRyi/KTlFNB17g7Scgp4tUcjxnRuoHZIRnmFOlrN/J3MvCK+G9maBxp6qh2SEEIILPvzW9WetoKCAqKioujatatxm1arpWvXruzdu/eWz1mzZg1t27ZlzJgxeHt707RpU2bNmoVOp7vl/jqdjmXLlpGTk0Pbtm0BiIqKorCwsMRxg4KCCAgIuO1xhfnxcLJl6iPBAMzbepzTV3JUjugfv8VfIjOviJruDrSrX13tcIQQQlQCqiZtV65cQafT4e3tXWK7t7c3KSkpt3zOqVOn+Pnnn9HpdGzYsIGpU6fy0UcfMWPGjBL7xcXF4ezsjJ2dHS+88AKrVq2icWPDGpYpKSnY2tri7u5e6uPm5+eTmZlZ4ibU1695TTo09KSgSM8bK+NQuePYqHgCwsCwmmilNpsQQohyoPo1bWWl1+vx8vLi66+/Jjw8nMGDBzNlyhQWLFhQYr9GjRoRExNDZGQko0aNYtiwYcTH33tB1tmzZ+Pm5ma8+fvLbEBzoNFomNkvBHsbLXtPXWXF38mSmpIzrrPruKE220CpzSaEEKKcqJq0eXp6YmVlddOszUuXLuHj43PL5/j6+hIYGIiVlZVxW3BwMCkpKRQUFBi32dra0qBBA8LDw5k9ezbNmjVj3rx5APj4+FBQUEB6enqpjzt58mQyMjKMt3PnLGv9y8osoLojE7oZlriauT6By1n5qsaz8uAFFAVa1fWgdnUnVWMRQghReaiatNna2hIeHs7WrVuN2/R6PVu3bjVef/Zv7du358SJE+j1/8wWPHbsGL6+vtja2t72WHq9nvx8w4d5eHg4NjY2JY6bmJhIUlLSbY9rZ2dnLCFSfBPm4+n2dWni50rG9UKmr1NviauStdmkl00IIUT5UX14dMKECSxcuJAlS5aQkJDAqFGjyMnJYcSIEQAMHTqUyZMnG/cfNWoUaWlpjBs3jmPHjrF+/XpmzZrFmDFjjPtMnjyZnTt3cubMGeLi4pg8eTLbt29nyJAhALi5uTFy5EgmTJjAtm3biIqKYsSIEbRt27ZUM0eF+bG20vLegFC0Glh76CLbjt66ZExFizp7jdNXcnC0taJ3iNRmE0IIUX6s1Q5g8ODBXL58mWnTppGSkkLz5s3ZtGmTcXJCUlISWu0/uaW/vz+bN2/m5ZdfJjQ0lJo1azJu3Dhef/114z6pqakMHTqU5ORk3NzcCA0NZfPmzXTr1s24z8cff4xWq2XgwIHk5+fTo0cPvvzyS9O9cFHuQmq5MfKBuizcdZo3fz3Mby8/iJOdaX/Ei3vZeoX4mvzYQgghKjfV67RZKkuu81KZ5RYU0f3jnZy/dp2RD9Rl6iONTXrsVjO3kp1fxLLn2tCmnpT6EEIIc2PJn9+qD48KUZ4cba2Z0c+wxNWiPac5dC7dZMfedDiF7PwiAjwcaV3Xw2THFUIIUTVI0iYqnU6NvOjb3A+9ApNMuMTVjRMQNBqpzSaEEKJ8SdImKqWpjzTG3dGGhORMvt19usKPdy4tlz9PXkWjgQFhNe/+BCGEEKKMJGkTlZKnsx1v9jZcz/bxlmOcvVqxS1z9ctDQy9aufnVqVXOs0GMJIYSomiRpE5XWwLCatG9QnfwiPW+sqrglrvR6xZi0SW02IYQQFUWSNlFpFS9xZWetZc+Jq6w8eKFCjhN5Oo1zaddxsbOmZxOpzSaEEKJiSNImKrU6nk6M72pY4mrG+niuZpf/ElfFExAeaeaLg63VXfYWQggh7o0kbaLSe6ZDXYJ9XbmWW8iM9Qnl2nZ2fhEb4pIBGRoVQghRsSRpE5WejZWW9waEoNHAqugL7Dh2udza3hCbzPVCHfU8nQgLqFZu7QohhBD/JkmbqBKa+bszvF0dAKasiiO3oKhc2i0eGh0otdmEEEJUMEnaRJXxSvdG1HR34Py163zy+/H7bu/MlRz2nUlDq4GBYTI0KoQQomJJ0iaqDCe7f5a4+mbXKQ5fyLiv9op72To0rIGPm/19xyeEEELciSRtokrpHOTFI6G+fy9xFUvRPS5xpZPabEIIIUxMkjZR5bzVpwmu9tYcvpDJoj1n7qmNP09eITkjD1d7a7o19i7fAIUQQohbkKRNVDk1XOyY0jsYgLlbjnEuLbfMbRQPjfZtXhN7G6nNJoQQouJJ0iaqpEEt/WlTz4PrhTqm/Hq4TEtcZVwvZNPhFECGRoUQQpiOJG2iStJoNMzqH4KttZadxy6zOuZiqZ+7LvYi+UV6Ar2dCa3lVoFRCiGEEP+QpE1UWfVqODP2oQYATF8XT1pOQameVzw0+pjUZhNCCGFCkrSJKu25B+vTyNuFtJwCZpZiiasTqVlEJ6VjpdXQr0VNE0QohBBCGEjSJqo0W2stswcalrj65eB5dh+/csf9V/zdy9a5UQ28XKQ2mxBCCNORpE1UeWEB1RjapjYAb6yK43qB7pb7Fen0rDp4AZAJCEIIIUxPkjYhgFd7BuHrZk9SWi7ztt56iatdx6+QmpWPh5MtDwVJbTYhhBCmJUmbEICznTXT+xqWuFq46xRHLt68xNU/tdn8sLWWXx0hhBCmJZ88QvytW2NveoX4oNMrTF4Zh07/T+229NwCtsRfAmRoVAghhDokaRPiBm/3aYKLvTWx5zNY/OcZ4/bVMRcp0Olp7OtKEz+pzSaEEML0JGkT4gZervZMftiwxNVHvyVy/pphiasba7MJIYQQapCkTYh/+U+EP63qeJBboGPqr4c5mpJJ3IUMbKykNpsQQgj1SNImxL9otRpmDQjB1krLtsTLjF8WA0CXIG88nGzVDU4IIUSVJUmbELfQwMuZMZ0NS1wdTckCZGhUCCGEuiRpE+I2RnWqT0MvZwA8ne3o1KiGyhEJIYSoyiRpE+I2bK21zHm8GXWqOzKua0OsreTXRQghhHqs1Q5ACHPWzN+d7a92VjsMIYQQQnrahBBCCCEsgSRtQgghhBAWQJI2IYQQQggLIEmbEEIIIYQFkKRNCCGEEMICSNImhBBCCGEBJGkTQgghhLAAkrQJIYQQQlgASdqEEEIIISyAJG1CCCGEEBZAkjYhhBBCCAsgSZsQQgghhAWQpE0IIYQQwgJI0iaEEEIIYQGs1Q7AUimKAkBmZqbKkQghhBCitIo/t4s/xy2JJG33KCsrCwB/f3+VIxFCCCFEWWVlZeHm5qZ2GGWiUSwx1TQDer2eixcv4uLigkajUTscs5SZmYm/vz/nzp3D1dVV7XCqPDkf5kXOh3mR82FeKvJ8KIpCVlYWfn5+aLWWdZWY9LTdI61WS61atdQOwyK4urrKH0EzIufDvMj5MC9yPsxLRZ0PS+thK2ZZKaYQQgghRBUlSZsQQgghhAWQpE1UGDs7O9566y3s7OzUDkUg58PcyPkwL3I+zIucj1uTiQhCCCGEEBZAetqEEEIIISyAJG1CCCGEEBZAkjYhhBBCCAsgSZsQQgghhAWQpE3cl9mzZxMREYGLiwteXl7069ePxMTEEvvk5eUxZswYqlevjrOzMwMHDuTSpUsqRVy1vPfee2g0GsaPH2/cJufDtC5cuMCTTz5J9erVcXBwICQkhAMHDhgfVxSFadOm4evri4ODA127duX48eMqRlx56XQ6pk6dSt26dXFwcKB+/fq8++67JdaglPNRsXbu3EmfPn3w8/NDo9Hw66+/lni8NO9/WloaQ4YMwdXVFXd3d0aOHEl2drYJX4V6JGkT92XHjh2MGTOGv/76iy1btlBYWEj37t3Jyckx7vPyyy+zdu1aVqxYwY4dO7h48SIDBgxQMeqqYf/+/Xz11VeEhoaW2C7nw3SuXbtG+/btsbGxYePGjcTHx/PRRx9RrVo14z4ffPABn376KQsWLCAyMhInJyd69OhBXl6eipFXTu+//z7z58/n888/JyEhgffff58PPviAzz77zLiPnI+KlZOTQ7Nmzfjiiy9u+Xhp3v8hQ4Zw5MgRtmzZwrp169i5cyfPPfecqV6CuhQhylFqaqoCKDt27FAURVHS09MVGxsbZcWKFcZ9EhISFEDZu3evWmFWellZWUrDhg2VLVu2KB07dlTGjRunKIqcD1N7/fXXlQceeOC2j+v1esXHx0f58MMPjdvS09MVOzs75ccffzRFiFVK7969laeffrrEtgEDBihDhgxRFEXOh6kByqpVq4z3S/P+x8fHK4Cyf/9+4z4bN25UNBqNcuHCBZPFrhbpaRPlKiMjAwAPDw8AoqKiKCwspGvXrsZ9goKCCAgIYO/evarEWBWMGTOG3r17l3jfQc6Hqa1Zs4aWLVvy+OOP4+XlRYsWLVi4cKHx8dOnT5OSklLifLi5udG6dWs5HxWgXbt2bN26lWPHjgFw6NAhdu/ezcMPPwzI+VBbad7/vXv34u7uTsuWLY37dO3aFa1WS2RkpMljNjVZMF6UG71ez/jx42nfvj1NmzYFICUlBVtbW9zd3Uvs6+3tTUpKigpRVn7Lli3j4MGD7N+//6bH5HyY1qlTp5g/fz4TJkzgjTfeYP/+/YwdOxZbW1uGDRtmfM+9vb1LPE/OR8WYNGkSmZmZBAUFYWVlhU6nY+bMmQwZMgRAzofKSvP+p6Sk4OXlVeJxa2trPDw8qsQ5kqRNlJsxY8Zw+PBhdu/erXYoVda5c+cYN24cW7Zswd7eXu1wqjy9Xk/Lli2ZNWsWAC1atODw4cMsWLCAYcOGqRxd1fPTTz/x/fff88MPP9CkSRNiYmIYP348fn5+cj6ERZDhUVEuXnzxRdatW8e2bduoVauWcbuPjw8FBQWkp6eX2P/SpUv4+PiYOMrKLyoqitTUVMLCwrC2tsba2podO3bw6aefYm1tjbe3t5wPE/L19aVx48YltgUHB5OUlARgfM//PXtXzkfFePXVV5k0aRL/+c9/CAkJ4amnnuLll19m9uzZgJwPtZXm/ffx8SE1NbXE40VFRaSlpVWJcyRJm7gviqLw4osvsmrVKv744w/q1q1b4vHw8HBsbGzYunWrcVtiYiJJSUm0bdvW1OFWel26dCEuLo6YmBjjrWXLlgwZMsT4fzkfptO+ffubSuAcO3aM2rVrA1C3bl18fHxKnI/MzEwiIyPlfFSA3NxctNqSH3tWVlbo9XpAzofaSvP+t23blvT0dKKiooz7/PHHH+j1elq3bm3ymE1O7ZkQwrKNGjVKcXNzU7Zv364kJycbb7m5ucZ9XnjhBSUgIED5448/lAMHDiht27ZV2rZtq2LUVcuNs0cVRc6HKe3bt0+xtrZWZs6cqRw/flz5/vvvFUdHR+W7774z7vPee+8p7u7uyurVq5XY2Filb9++St26dZXr16+rGHnlNGzYMKVmzZrKunXrlNOnTysrV65UPD09lddee824j5yPipWVlaVER0cr0dHRCqDMnTtXiY6OVs6ePasoSune/549eyotWrRQIiMjld27dysNGzZUnnjiCbVekklJ0ibuC3DL26JFi4z7XL9+XRk9erRSrVo1xdHRUenfv7+SnJysXtBVzL+TNjkfprV27VqladOmip2dnRIUFKR8/fXXJR7X6/XK1KlTFW9vb8XOzk7p0qWLkpiYqFK0lVtmZqYybtw4JSAgQLG3t1fq1aunTJkyRcnPzzfuI+ejYm3btu2WnxnDhg1TFKV07//Vq1eVJ554QnF2dlZcXV2VESNGKFlZWSq8GtPTKMoNpaCFEEIIIYRZkmvahBBCCCEsgCRtQgghhBAWQJI2IYQQQggLIEmbEEIIIYQFkKRNCCGEEMICSNImhBBCCGEBJGkTooorKChg1qxZJCQkqB2KMLG8vDxmzJhBXFyc2qEIIUpBkjYh7qBOnTp88sknxvsajYZff/0VgDNnzqDRaIiJiSnXYy5evBh3d/f7amP48OH069evVPtOnDiRuLg4goKCSt1+p06dGD9+/L0Fd5/+/b5v374djUZjXE+1PN6/e/XvnxdzN23aNP7880+eeuopCgoK1A7H5KZOncpzzz13X21MmjSJl156qZwiEuLOJGkTlZpGo7nj7e23377j8/fv33/ff9TNwfDhw2/5Wn/66SeOHDnCkiVL0Gg0pg/sLm6VfPr7+5OcnEzTpk3VCaoM6tSpw/bt28u9zfJIDPft20dkZCRr1qxh0KBBd/1dsARl+SKVkpLCvHnzmDJlinHb999/j7+/P9WqVWPChAk3tR0YGEhmZmaJ7a+88gpLlizh1KlT5fIahLgTa7UDEKIiJScnG/+/fPlypk2bVmIBb2dn5zs+v0aNGhUWmzkYNGgQgwYNUjuMMrGyssLHx0ftMMyaTqdDo9HctDj6jVq1asWOHTsAeOONN0wVmtn45ptvaNeuHbVr1wbgypUrPPPMMyxevJh69erRu3dvHnroIR555BEARo8ezXvvvYerq2uJdjw9PenRowfz58/nww8/NPnrEFWL9LSJSs3Hx8d4c3NzQ6PRGO/n5OQwZMgQvL29cXZ2JiIigt9//73E88vaq3H48GEefvhhnJ2d8fb25qmnnuLKlSt3fM7ixYsJCAjA0dGR/v37c/Xq1Zv2Wb16NWFhYdjb21OvXj3eeecdioqKSh3Xv+Xn5/PKK69Qs2ZNnJycaN269U09Qnv27KFTp044OjpSrVo1evTowbVr14yP6/V6XnvtNTw8PPDx8bmpp2bu3LmEhITg5OSEv78/o0ePJjs7u8Trdnd3Z/PmzQQHB+Ps7EzPnj2Nifbbb7/NkiVLWL16tbFndPv27fc0LD1//nzq16+Pra0tjRo1YunSpSUe12g0fPPNN/Tv3x9HR0caNmzImjVr7thmamoqffr0wcHBgbp16/L999/fNY5z584xaNAg3N3d8fDwoG/fvpw5c8b4eHHP4pw5c/D19aV69eqMGTOGwsJCwDAsffbsWV5++WXje3Lje7lmzRoaN26MnZ0dSUlJ7N+/n27duuHp6YmbmxsdO3bk4MGDN732fw/5r1y5ks6dO+Po6EizZs3Yu3dviefs3r2bDh064ODggL+/P2PHjiUnJ8f4eJ06dZgxYwZDhw7F2dmZ2rVrs2bNGi5fvkzfvn1xdnYmNDSUAwcOlLndWbNm8fTTT+Pi4kJAQABff/218fG6desC0KJFCzQaDZ06dbrtuVi2bBl9+vQx3j916hRubm4MHjyYiIgIOnfubLzO88cff8TGxoYBAwbcsq0+ffqwbNmy2x5LiPIiSZuosrKzs+nVqxdbt24lOjqanj170qdPH5KSku6pvfT0dB566CFatGjBgQMH2LRpE5cuXbpjT1ZkZCQjR47kxRdfJCYmhs6dOzNjxowS++zatYuhQ4cybtw44uPj+eqrr1i8eDEzZ868pzgBXnzxRfbu3cuyZcuIjY3l8ccfp2fPnhw/fhyAmJgYunTpQuPGjdm7dy+7d++mT58+6HQ6YxtLlizBycmJyMhIPvjgA6ZPn86WLVuMj2u1Wj799FPj8Osff/zBa6+9ViKO3Nxc5syZw9KlS9m5cydJSUm88sorgGHYadCgQcZELjk5mXbt2pX5ta5atYpx48YxceJEDh8+zPPPP8+IESPYtm1bif3eeecdBg0aRGxsLL169WLIkCGkpaXdtt3hw4dz7tw5tm3bxs8//8yXX35JamrqbfcvLCykR48euLi4sGvXLvbs2WNMVG+8nmzbtm2cPHmSbdu2sWTJEhYvXszixYsBWLlyJbVq1WL69OnG9+TG9/L999/nm2++4ciRI3h5eZGVlcWwYcPYvXs3f/31Fw0bNqRXr15kZWXd8T2bMmUKr7zyCjExMQQGBvLEE08YvyScPHmSnj17MnDgQGJjY1m+fDm7d+/mxRdfLNHGxx9/TPv27YmOjqZ379489dRTDB06lCeffJKDBw9Sv359hg4dSvHy16Vt96OPPqJly5ZER0czevRoRo0aZew937dvHwC///47ycnJrFy58pavLy0tjfj4eFq2bGnc1rBhQ3Jzc4mOjiYtLY39+/cTGhrKtWvXmDp1Kp9//vlt369WrVpx/vz5Egm4EBVC5QXrhTCZRYsWKW5ubnfcp0mTJspnn31mvF+7dm3l448/Nt4HlFWrVimKoiinT59WACU6OlpRFEV59913le7du5do79y5cwqgJCYm3vJ4TzzxhNKrV68S2wYPHlwizi5duiizZs0qsc/SpUsVX1/f276OYcOGKX379r3lY2fPnlWsrKyUCxculNjepUsXZfLkyca42rdvf9v2O3bsqDzwwAMltkVERCivv/76bZ+zYsUKpXr16sb7ixYtUgDlxIkTxm1ffPGF4u3tfcfX8e/3fdu2bQqgXLt2zdjuje9fu3btlGeffbZEG48//niJ9x1Q3nzzTeP97OxsBVA2btx4y9eSmJioAMq+ffuM2xISEhSgxM/LjZYuXao0atRI0ev1xm35+fmKg4ODsnnzZuPrrV27tlJUVFQi1sGDBxvv//tnsvg1A0pMTMwtj11Mp9MpLi4uytq1a0u89n//TH/zzTfGx48cOaIASkJCgqIoijJy5EjlueeeK9Hurl27FK1Wq1y/ft0Y45NPPml8PDk5WQGUqVOnGrft3btXAZTk5OR7blev1yteXl7K/PnzS8Rf/LNxO9HR0QqgJCUlldi+cuVKpWnTpkr9+vWVt956S1EURXn66aeVjz/+WNmxY4fSvHlzpUmTJsqKFStKPC8jI0MBlO3bt9/xuELcL7mmTVRZ2dnZvP3226xfv57k5GSKioq4fv36Pfe0HTp0iG3btt3yOrmTJ08SGBh40/aEhAT69+9fYlvbtm3ZtGlTiXb37NlTomdNp9ORl5dHbm4ujo6OZYozLi4OnU53Uzz5+flUr14dMPS0Pf7443dsJzQ0tMR9X1/fEj1Nv//+O7Nnz+bo0aNkZmZSVFR0U8yOjo7Ur1//tm2Uh4SEhJsmk7Rv35558+bd9vU4OTnh6up621gSEhKwtrYmPDzcuC0oKOiOs1YPHTrEiRMncHFxKbE9Ly+PkydPGu83adIEKysr431fX99SleSwtbW96ZxcunSJN998k+3bt5OamopOpyM3N/euP+M3tuPr6wsYhoODgoI4dOgQsbGxJYaDFUVBr9dz+vRpgoODb2rD29sbgJCQkJu2paam4uPjc0/tFl/uUNafmevXrwNgb29fYnv//v1L/D7u2LGD2NhYPvvsMxo0aMCPP/6Ij48PrVq14sEHH8TLywsABwcHwNDbKURFkqRNVFmvvPIKW7ZsYc6cOTRo0AAHBwcee+yxey59kJ2dTZ8+fXj//fdveqz4g+9e233nnXdueT3Nvz90StuelZUVUVFRJZID+GdiRvGH0J3Y2NiUuK/RaNDr9YDh2qhHHnmEUaNGMXPmTDw8PNi9ezcjR46koKDAmLTdqg3l7+EyU7vT6ykP2dnZhIeH3/LatxsnvNxrHA4ODjfNAB42bBhXr15l3rx51K5dGzs7O9q2bXvXn/EbYyhusziG7Oxsnn/+ecaOHXvT8wICAu7YRnm3W9xOWc+Tp6cnANeuXbvtZKP8/HxGjx7N0qVLOXHiBEVFRXTs2BGAwMBAIiMjjdfEFQ+jV/aJS0J9krSJKmvPnj0MHz7c+M06Ozv7vq5JCQsL45dffqFOnTpYW5fuVys4OJjIyMgS2/7666+b2k1MTKRBgwb3HNuNWrRogU6nIzU1lQ4dOtxyn9DQULZu3co777xzT8eIiopCr9fz0UcfGWcw/vTTT2Vux9bWtsR1dPciODiYPXv2MGzYMOO2PXv20Lhx43tuMygoiKKiIqKiooiIiAAgMTHRWCvuVsLCwli+fDleXl43zUAsi7K8J3v27OHLL7+kV69egGEixN0mxtxNWFgY8fHx5fbzWJ7t2traAtz1/alfvz6urq7Ex8ffsgccYMaMGfTs2ZOwsDCio6NLTPwpLCwscYzDhw9jY2NDkyZN7jl2IUpDJiKIKqthw4asXLmSmJgYDh06xH//+9/76lkZM2YMaWlpPPHEE+zfv5+TJ0+yefNmRowYcdsPkbFjx7Jp0ybmzJnD8ePH+fzzz0sMjYKhAOr//d//8c4773DkyBESEhJYtmwZb7755j3FGRgYyJAhQxg6dCgrV67k9OnT7Nu3j9mzZ7N+/XoAJk+ezP79+xk9ejSxsbEcPXqU+fPnl/oDv0GDBhQWFvLZZ59x6tQpli5dyoIFC8oca506dYiNjSUxMZErV64YZ1GWxauvvsrixYuZP38+x48fZ+7cuaxcudI44eFeNGrUiJ49e/L8888TGRlJVFQUzzzzzB17KIcMGYKnpyd9+/Zl165dnD59mu3btzN27FjOnz9f6mPXqVOHnTt3cuHChbuej4YNG7J06VISEhKIjIxkyJAhpepFvZPXX3+dP//80zh55vjx46xevfqmCQNqtOvl5YWDg4NxElBGRsYt99NqtXTt2pXdu3ff8vH4+HiWL1/O9OnTAUOSrtVq+fbbb1m/fj1Hjx41JutgmCxUPOtViIokSZuosubOnUu1atVo164dffr0oUePHoSFhd1ze35+fuzZswedTkf37t0JCQlh/PjxuLu737ZeVps2bVi4cCHz5s2jWbNm/PbbbzclYz169GDdunX89ttvRERE0KZNGz7++GNjfal7sWjRIoYOHcrEiRNp1KgR/fr1Y//+/cZhqMDAQH777TcOHTpEq1ataNu2LatXry51D2KzZs2YO3cu77//Pk2bNuX7779n9uzZZY7z2WefpVGjRrRs2ZIaNWqwZ8+eMrfRr18/5s2bx5w5c2jSpAlfffUVixYtumM5iNJYtGgRfn5+dOzYkQEDBvDcc88Zr3G6FUdHR3bu3ElAQAADBgwgODiYkSNHkpeXV6aet+nTp3PmzBnq169/1+G4b7/9lmvXrhEWFsZTTz3F2LFj7xhjaYSGhrJjxw6OHTtGhw4daNGiBdOmTcPPz0/1dq2trfn000/56quv8PPzo2/fvrfd95lnnmHZsmU3fVFTFIXnnnuOuXPn4uTkBBiGnhcvXsz06dMZOXIkn3/+OTVr1jQ+Z9myZTz77LNlfMVClJ1GUesCEiGEEEIliqLQunVrXn75ZZ544ol7bmfjxo1MnDiR2NjYUn+pEeJeSU+bEEKIKkej0fD111/fV5FqgJycHBYtWiQJmzAJ6WkTQgghhLAA0tMmhBBCCGEBJGkTQgghhLAAkrQJIYQQQlgASdqEEEIIISyAJG1CCCGEEBZAkjYhhBBCCAsgSZsQQgghhAWQpE0IIYQQwgJI0iaEEEIIYQEkaRNCCCGEsAD/D8LN2ky8ch9nAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from lightgbm import LGBMClassifier, early_stopping, log_evaluation\n", "from sklearn.metrics import f1_score\n", "from sklearn.model_selection import train_test_split\n", "\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42\n", ")\n", "\n", "train_scores = []\n", "test_scores = []\n", "train_sizes = np.linspace(0.1, 1.0, 10)  # 10% à 100% des données d'entraînement\n", "\n", "for frac in train_sizes:\n", "    n_samples = int(len(X_train) * frac)\n", "    X_sub, y_sub = X_train[:n_samples], y_train[:n_samples]\n", "\n", "    model = LGBMClassifier(\n", "        n_estimators=1000,\n", "        random_state=42\n", "    )\n", "\n", "    model.fit(\n", "        X_sub, y_sub,\n", "        eval_set=[(X_test, y_test)],\n", "        callbacks=[early_stopping(stopping_rounds=50), log_evaluation(period=0)]  # period=0 pour pas de logs\n", "    )\n", "\n", "    y_sub_pred = model.predict(X_sub)\n", "    y_test_pred = model.predict(X_test)\n", "\n", "    train_scores.append(f1_score(y_sub, y_sub_pred, average='macro'))\n", "    test_scores.append(f1_score(y_test, y_test_pred, average='macro'))\n", "\n", "plt.plot(train_sizes * 100, train_scores, label=\"Score d'entraînement (F1 macro)\")\n", "plt.plot(train_sizes * 100, test_scores, label=\"Score de validation (F1 macro)\")\n", "plt.xlabel(\"Taille de l'échantillon d'entraînement (%)\")\n", "plt.ylabel(\"Score F1 macro\")\n", "plt.title(\"Courbe d'apprentissage avec LightGBM et early stopping (callbacks)\")\n", "plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "3c35b2b4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.61      0.83      0.70     99450\n", "           1       0.97      0.98      0.97     15534\n", "           2       0.62      0.50      0.56     68879\n", "           3       0.00      0.00      0.00     29472\n", "           4       0.78      0.95      0.86     21717\n", "\n", "    accuracy                           0.65    235052\n", "   macro avg       0.59      0.65      0.62    235052\n", "weighted avg       0.57      0.65      0.60    235052\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, ConfusionMatrixDisplay\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "\n", "# Initialiser et entraîner le modèle\n", "tree_model = DecisionTreeClassifier(\n", "    max_depth=4,       # Limite la profondeur de l'arbre (à ajuster selon ton besoin)\n", "    random_state=42\n", ")\n", "\n", "tree_model.fit(X_train, y_train)\n", "\n", "# Prédiction\n", "y_pred = tree_model.predict(X_test)\n", "\n", "# Évaluation\n", "print(classification_report(y_test, y_pred))\n", "\n", "# Afficher la matrice de confusion\n", "ConfusionMatrixDisplay.from_estimator(tree_model, X_test, y_test)\n", "plt.title(\"<PERSON><PERSON> de confusion - <PERSON><PERSON><PERSON> de Décision\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "a5019173", "metadata": {}, "source": ["Balancing"]}, {"cell_type": "code", "execution_count": 6, "id": "d56d637f", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [14:44:02] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📘 XGBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.56      0.61     99477\n", "           1       0.96      0.98      0.97     15461\n", "           2       0.60      0.57      0.59     68800\n", "           3       0.21      0.30      0.25     29488\n", "           4       0.74      0.98      0.84     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.65    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.004443 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 940210, number of used features: 4\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] Accuracy may be bad since you didn't explicitly set num_leaves OR 2^max_depth > num_leaves. (num_leaves=31).\n", "📗 LightGBM Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.56      0.62     99477\n", "           1       0.96      0.98      0.97     15461\n", "           2       0.60      0.58      0.59     68800\n", "           3       0.22      0.30      0.25     29488\n", "           4       0.75      0.97      0.85     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.65    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "📙 CatBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.56      0.61     99477\n", "           1       0.95      0.99      0.97     15461\n", "           2       0.60      0.58      0.59     68800\n", "           3       0.21      0.31      0.25     29488\n", "           4       0.75      0.97      0.85     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.65    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "📕 Random Forest Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00     99477\n", "           1       0.96      0.99      0.97     15461\n", "           2       0.76      0.11      0.19     68800\n", "           3       0.13      0.82      0.23     29488\n", "           4       0.74      0.98      0.84     21827\n", "\n", "    accuracy                           0.29    235053\n", "   macro avg       0.52      0.58      0.45    235053\n", "weighted avg       0.37      0.29      0.23    235053\n", "\n", "\n", "📊 Comparaison des modèles :\n", "           Model  F1_macro  Accuracy\n", "1       LightGBM  0.654864  0.601209\n", "2       CatBoost  0.653734  0.599214\n", "0        XGBoost  0.653561  0.599201\n", "3  Random Forest  0.447337  0.290879\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score, classification_report\n", "from sklearn.utils.class_weight import compute_sample_weight\n", "from xgboost import XGBClassifier\n", "from lightgbm import LGBMClassifier\n", "from catboost import CatBoostClassifier, Pool\n", "\n", "# ------------------------------\n", "# Données déjà préparées\n", "# ------------------------------\n", "# X_train, X_test, y_train, y_test doivent déjà être définis\n", "# Exemple :\n", "# from sklearn.model_selection import train_test_split\n", "# X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y, test_size=0.2, random_state=42)\n", "\n", "# Poids des échantillons\n", "sample_weights = compute_sample_weight(class_weight='balanced', y=y_train)\n", "\n", "# Résultats\n", "results = []\n", "\n", "# ------------------------------\n", "# 1. XGBoost\n", "# ------------------------------\n", "xgb = XGBClassifier(\n", "    objective='multi:softprob',\n", "    num_class=5,\n", "    use_label_encoder=False,\n", "    eval_metric='mlogloss',\n", "    learning_rate=0.1,\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42\n", ")\n", "xgb.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_xgb = xgb.predict(X_test)\n", "results.append({\n", "    'Model': 'XGBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_xgb, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_xgb)\n", "})\n", "print(\"📘 XGBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_xgb))\n", "\n", "\n", "# ------------------------------\n", "# 2. LightGBM\n", "# ------------------------------\n", "lgbm = LGBMClassifier(\n", "    objective='multiclass',\n", "    num_class=5,\n", "    learning_rate=0.1,\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42\n", ")\n", "lgbm.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_lgbm = lgbm.predict(X_test)\n", "results.append({\n", "    'Model': 'LightGBM',\n", "    'F1_macro': f1_score(y_test, y_pred_lgbm, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_lgbm)\n", "})\n", "print(\"📗 LightGBM Classification Report:\")\n", "print(classification_report(y_test, y_pred_lgbm))\n", "\n", "\n", "# ------------------------------\n", "# 3. <PERSON><PERSON><PERSON><PERSON>\n", "# ------------------------------\n", "train_pool = Pool(X_train, y_train, weight=sample_weights)\n", "catboost = CatBoostClassifier(\n", "    iterations=100,\n", "    learning_rate=0.1,\n", "    depth=6,\n", "    loss_function='MultiClass',\n", "    random_seed=42,\n", "    verbose=0\n", ")\n", "catboost.fit(train_pool)\n", "y_pred_cat = catboost.predict(X_test).flatten()\n", "results.append({\n", "    'Model': 'CatBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_cat, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_cat)\n", "})\n", "print(\"📙 CatBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_cat))\n", "\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "# ------------------------------\n", "# 4. <PERSON>\n", "# ------------------------------\n", "rf = RandomForestClassifier(\n", "    n_estimators=100,\n", "    max_depth=6,\n", "    random_state=42,\n", "    class_weight='balanced'  # prend en compte l'équilibrage des classes\n", ")\n", "rf.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_rf = rf.predict(X_test)\n", "results.append({\n", "    'Model': 'Random Forest',\n", "    'F1_macro': f1_score(y_test, y_pred_rf, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_rf)\n", "})\n", "print(\"📕 Random Forest Classification Report:\")\n", "print(classification_report(y_test, y_pred_rf))\n", "\n", "# ------------------------------\n", "# R<PERSON><PERSON><PERSON> des performances\n", "# ------------------------------\n", "df_results = pd.DataFrame(results)\n", "print(\"\\n📊 Comparaison des modèles :\")\n", "print(df_results.sort_values(by='F1_macro', ascending=False))"]}, {"cell_type": "code", "execution_count": 12, "id": "61039714", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best RF: {'max_depth': 8, 'min_samples_split': 2, 'n_estimators': 100} Score: 0.4737630205972083\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\linear_model\\_sag.py:350: ConvergenceWarning: The max_iter was reached which means the coef_ did not converge\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best LR: {'C': 0.01, 'penalty': 'l2', 'solver': 'saga'} Score: 0.4058241371267606\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [14:55:59] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best XGB: {'learning_rate': 0.1, 'max_depth': 8, 'n_estimators': 200} Score: 0.6525590041090865\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.007359 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 940210, number of used features: 4\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "Best LGBM: {'learning_rate': 0.1, 'max_depth': -1, 'n_estimators': 200} Score: 0.6539975275059889\n", "Best CatBoost: {'depth': 8, 'iterations': 500, 'learning_rate': 0.1} Score: 0.6538216666252961\n"]}], "source": ["from sklearn.model_selection import GridSearchCV\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from xgboost import XGBClassifier\n", "from lightgbm import LGBMClassifier\n", "from catboost import CatBoostClassifier\n", "\n", "# ------------------------------\n", "# 1. <PERSON> Forest\n", "# ------------------------------\n", "param_grid_rf = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [4, 6, 8],\n", "    'min_samples_split': [2, 5],\n", "}\n", "grid_rf = GridSearchCV(\n", "    RandomForestClassifier(class_weight=\"balanced\", random_state=42),\n", "    param_grid=param_grid_rf,\n", "    cv=3,\n", "    scoring='f1_macro',\n", "    n_jobs=-1\n", ")\n", "grid_rf.fit(X_train, y_train, sample_weight=sample_weights)\n", "print(\"Best RF:\", grid_rf.best_params_, \"Score:\", grid_rf.best_score_)\n", "\n", "\n", "# ------------------------------\n", "# 2. <PERSON><PERSON><PERSON>\n", "# ------------------------------\n", "param_grid_lr = {\n", "    'C': [0.01, 0.1, 1, 10],\n", "    'solver': ['liblinear', 'saga'],\n", "    'penalty': ['l1', 'l2']\n", "}\n", "grid_lr = GridSearchCV(\n", "    LogisticRegression(max_iter=1000, class_weight=\"balanced\", random_state=42),\n", "    param_grid=param_grid_lr,\n", "    cv=3,\n", "    scoring='f1_macro',\n", "    n_jobs=-1\n", ")\n", "grid_lr.fit(X_train, y_train, sample_weight=sample_weights)\n", "print(\"Best LR:\", grid_lr.best_params_, \"Score:\", grid_lr.best_score_)\n", "\n", "\n", "# ------------------------------\n", "# 3. XGBoost\n", "# ------------------------------\n", "param_grid_xgb = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [4, 6, 8],\n", "    'learning_rate': [0.05, 0.1],\n", "}\n", "grid_xgb = GridSearchCV(\n", "    XGBClassifier(\n", "        objective=\"multi:softprob\",\n", "        num_class=len(y_train.unique()),\n", "        use_label_encoder=False,\n", "        eval_metric=\"mlogloss\",\n", "        random_state=42\n", "    ),\n", "    param_grid=param_grid_xgb,\n", "    cv=3,\n", "    scoring='f1_macro',\n", "    n_jobs=-1\n", ")\n", "grid_xgb.fit(X_train, y_train, sample_weight=sample_weights)\n", "print(\"Best XGB:\", grid_xgb.best_params_, \"Score:\", grid_xgb.best_score_)\n", "\n", "\n", "# ------------------------------\n", "# 4. LightGBM\n", "# ------------------------------\n", "param_grid_lgbm = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [4, 6, -1],\n", "    'learning_rate': [0.05, 0.1],\n", "}\n", "grid_lgbm = GridSearchCV(\n", "    LGBMClassifier(objective=\"multiclass\", random_state=42),\n", "    param_grid=param_grid_lgbm,\n", "    cv=3,\n", "    scoring='f1_macro',\n", "    n_jobs=-1\n", ")\n", "grid_lgbm.fit(X_train, y_train, sample_weight=sample_weights)\n", "print(\"Best LGBM:\", grid_lgbm.best_params_, \"Score:\", grid_lgbm.best_score_)\n", "\n", "\n", "# ------------------------------\n", "# 5. CatB<PERSON><PERSON>\n", "# ------------------------------\n", "param_grid_cat = {\n", "    'iterations': [200, 500],\n", "    'depth': [4, 6, 8],\n", "    'learning_rate': [0.05, 0.1],\n", "}\n", "grid_cat = GridSearchCV(\n", "    CatBoostClassifier(loss_function=\"MultiClass\", verbose=0, random_seed=42),\n", "    param_grid=param_grid_cat,\n", "    cv=3,\n", "    scoring='f1_macro',\n", "    n_jobs=-1\n", ")\n", "grid_cat.fit(X_train, y_train, sample_weight=sample_weights)\n", "print(\"Best CatBoost:\", grid_cat.best_params_, \"Score:\", grid_cat.best_score_)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1627ad86", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [22:34:48] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📘 XGBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.57      0.62     99477\n", "           1       0.96      0.98      0.97     15461\n", "           2       0.60      0.57      0.59     68800\n", "           3       0.21      0.30      0.25     29488\n", "           4       0.74      0.98      0.84     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.65    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.030187 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 940210, number of used features: 4\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "[LightGBM] [Info] Start training from score -1.609438\n", "📗 LightGBM Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.57      0.62     99477\n", "           1       0.96      0.98      0.97     15461\n", "           2       0.60      0.57      0.59     68800\n", "           3       0.22      0.30      0.25     29488\n", "           4       0.75      0.97      0.85     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.66    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "📙 CatBoost Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.57      0.62     99477\n", "           1       0.96      0.98      0.97     15461\n", "           2       0.60      0.58      0.59     68800\n", "           3       0.22      0.29      0.25     29488\n", "           4       0.74      0.97      0.84     21827\n", "\n", "    accuracy                           0.60    235053\n", "   macro avg       0.64      0.68      0.65    235053\n", "weighted avg       0.62      0.60      0.61    235053\n", "\n", "📕 Random Forest Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.69      0.00      0.00     99477\n", "           1       0.95      0.99      0.97     15461\n", "           2       0.74      0.20      0.32     68800\n", "           3       0.14      0.81      0.24     29488\n", "           4       0.74      0.97      0.84     21827\n", "\n", "    accuracy                           0.32    235053\n", "   macro avg       0.66      0.59      0.47    235053\n", "weighted avg       0.66      0.32      0.27    235053\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\linear_model\\_sag.py:350: ConvergenceWarning: The max_iter was reached which means the coef_ did not converge\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📒 Logistic Regression Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00     99477\n", "           1       0.93      1.00      0.96     15461\n", "           2       0.23      0.00      0.00     68800\n", "           3       0.13      0.85      0.23     29488\n", "           4       0.76      0.94      0.84     21827\n", "\n", "    accuracy                           0.26    235053\n", "   macro avg       0.41      0.56      0.41    235053\n", "weighted avg       0.22      0.26      0.17    235053\n", "\n", "\n", "📊 Comparaison des modèles :\n", "                 Model  F1_macro  Accuracy\n", "1             LightGBM  0.655025  0.602090\n", "2             CatBoost  0.654251  0.603260\n", "0              XGBoost  0.654218  0.601762\n", "3        Random Forest  0.474437  0.316316\n", "4  Logistic Regression  0.405896  0.259061\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1509: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score, classification_report\n", "from sklearn.utils.class_weight import compute_sample_weight\n", "from xgboost import XGBClassifier\n", "from lightgbm import LGBMClassifier\n", "from catboost import CatBoostClassifier, Pool\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "# ------------------------------\n", "# Données déjà préparées\n", "# ------------------------------\n", "# X_train, X_test, y_train, y_test doivent déjà être définis\n", "# Exemple :\n", "# from sklearn.model_selection import train_test_split\n", "# X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y, test_size=0.2, random_state=42)\n", "\n", "# Poids des échantillons\n", "sample_weights = compute_sample_weight(class_weight='balanced', y=y_train)\n", "\n", "# Résultats\n", "results = []\n", "\n", "# ------------------------------\n", "# 1. <PERSON><PERSON><PERSON><PERSON> (Best params)\n", "# ------------------------------\n", "xgb = XGBClassifier(\n", "    objective='multi:softprob',\n", "    num_class=5,\n", "    use_label_encoder=False,\n", "    eval_metric='mlogloss',\n", "    learning_rate=0.1,\n", "    n_estimators=200,   # Best\n", "    max_depth=8,        # Best\n", "    random_state=42\n", ")\n", "xgb.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_xgb = xgb.predict(X_test)\n", "results.append({\n", "    'Model': 'XGBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_xgb, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_xgb)\n", "})\n", "print(\"📘 XGBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_xgb))\n", "\n", "# ------------------------------\n", "# 2. LightGBM (Best params)\n", "# ------------------------------\n", "lgbm = LGBMClassifier(\n", "    objective='multiclass',\n", "    num_class=5,\n", "    learning_rate=0.1,\n", "    n_estimators=200,   # Best\n", "    max_depth=-1,       # Best\n", "    random_state=42\n", ")\n", "lgbm.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_lgbm = lgbm.predict(X_test)\n", "results.append({\n", "    'Model': 'LightGBM',\n", "    'F1_macro': f1_score(y_test, y_pred_lgbm, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_lgbm)\n", "})\n", "print(\"📗 LightGBM Classification Report:\")\n", "print(classification_report(y_test, y_pred_lgbm))\n", "\n", "# ------------------------------\n", "# 3. <PERSON><PERSON><PERSON><PERSON> (Best params)\n", "# ------------------------------\n", "train_pool = Pool(X_train, y_train, weight=sample_weights)\n", "catboost = CatBoostClassifier(\n", "    iterations=500,      # Best\n", "    learning_rate=0.1,   # Best\n", "    depth=8,             # Best\n", "    loss_function='MultiClass',\n", "    random_seed=42,\n", "    verbose=0\n", ")\n", "catboost.fit(train_pool)\n", "y_pred_cat = catboost.predict(X_test).flatten()\n", "results.append({\n", "    'Model': 'CatBoost',\n", "    'F1_macro': f1_score(y_test, y_pred_cat, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_cat)\n", "})\n", "print(\"📙 CatBoost Classification Report:\")\n", "print(classification_report(y_test, y_pred_cat))\n", "\n", "# ------------------------------\n", "# 4. <PERSON> (Best params)\n", "# ------------------------------\n", "rf = RandomForestClassifier(\n", "    n_estimators=100,   # Best\n", "    max_depth=8,        # Best\n", "    min_samples_split=2, # Best\n", "    random_state=42,\n", "    class_weight='balanced'\n", ")\n", "rf.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_rf = rf.predict(X_test)\n", "results.append({\n", "    'Model': 'Random Forest',\n", "    'F1_macro': f1_score(y_test, y_pred_rf, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_rf)\n", "})\n", "print(\"📕 Random Forest Classification Report:\")\n", "print(classification_report(y_test, y_pred_rf))\n", "\n", "# ------------------------------\n", "# 5. <PERSON><PERSON><PERSON> Regression (Best params)\n", "# ------------------------------\n", "lr = LogisticRegression(\n", "    C=0.01,             # Best\n", "    penalty='l2',       # Best\n", "    solver='saga',      # Best\n", "    max_iter=500,       # augmenté pour éviter ConvergenceWarning\n", "    class_weight='balanced',\n", "    random_state=42\n", ")\n", "lr.fit(X_train, y_train, sample_weight=sample_weights)\n", "y_pred_lr = lr.predict(X_test)\n", "results.append({\n", "    'Model': 'Logistic Regression',\n", "    'F1_macro': f1_score(y_test, y_pred_lr, average='macro'),\n", "    'Accuracy': accuracy_score(y_test, y_pred_lr)\n", "})\n", "print(\"📒 Logistic Regression Classification Report:\")\n", "print(classification_report(y_test, y_pred_lr))\n", "\n", "# ------------------------------\n", "# R<PERSON><PERSON><PERSON> des performances\n", "# ------------------------------\n", "df_results = pd.DataFrame(results)\n", "print(\"\\n📊 Comparaison des modèles :\")\n", "print(df_results.sort_values(by='F1_macro', ascending=False))\n"]}, {"cell_type": "code", "execution_count": null, "id": "fafdc964", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.model_selection import learning_curve\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Fonction pour tracer la courbe d'apprentissage d'un modèle\n", "def plot_learning_curve(model, model_name, X, y, scoring='f1_macro'):\n", "    train_sizes, train_scores, test_scores = learning_curve(\n", "        model,\n", "        X, y,\n", "        cv=5,\n", "        scoring=scoring,\n", "        n_jobs=-1,\n", "        train_sizes=np.linspace(0.1, 1.0, 5),\n", "        shuffle=True,\n", "        random_state=42\n", "    )\n", "\n", "    # Moyenne et écart-type\n", "    train_mean = np.mean(train_scores, axis=1)\n", "    train_std = np.std(train_scores, axis=1)\n", "    test_mean = np.mean(test_scores, axis=1)\n", "    test_std = np.std(test_scores, axis=1)\n", "\n", "    # Tracé\n", "    plt.figure(figsize=(8, 6))\n", "    plt.title(f\"Courbe d'apprentissage - {model_name}\")\n", "    plt.xlabel(\"Taille de l'échantillon d'entraînement\")\n", "    plt.y<PERSON><PERSON>(scoring)\n", "\n", "    plt.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.1, color=\"r\")\n", "    plt.fill_between(train_sizes, test_mean - test_std, test_mean + test_std, alpha=0.1, color=\"g\")\n", "\n", "    plt.plot(train_sizes, train_mean, 'o-', color=\"r\", label=\"Entraînement\")\n", "    plt.plot(train_sizes, test_mean, 'o-', color=\"g\", label=\"Test\")\n", "\n", "    plt.legend(loc=\"best\")\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "\n", "# Appel pour chaque modèle\n", "plot_learning_curve(xgb, \"XGBoost\", X_train, y_train)\n", "plot_learning_curve(lgbm, \"LightGBM\", X_train, y_train)\n", "plot_learning_curve(catboost, \"CatBoost\", X_train, y_train)\n", "plot_learning_curve(rf, \"Random Forest\", X_train, y_train)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1e096886", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DLMP</th>\n", "      <th>ARPU</th>\n", "      <th>IMPAYE</th>\n", "      <th>FRAUDE</th>\n", "      <th>CTX</th>\n", "      <th>Cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>121.0</td>\n", "      <td>13.795</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21.0</td>\n", "      <td>17.245</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6.0</td>\n", "      <td>9.775</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-12.0</td>\n", "      <td>43.918</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>535.0</td>\n", "      <td>23.573</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>753.0</td>\n", "      <td>22.728</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>-9.0</td>\n", "      <td>82.860</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>5.0</td>\n", "      <td>41.812</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>23.0</td>\n", "      <td>38.323</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>37.0</td>\n", "      <td>28.080</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 6 columns</p>\n", "</div>"], "text/plain": ["     DLMP    ARPU  IMPAYE  FRAUDE  CTX  Cluster\n", "0   121.0  13.795       0       0    0        2\n", "1    21.0  17.245       0       0    0        2\n", "2     6.0   9.775       1       0    0        2\n", "3   -12.0  43.918       0       0    0        0\n", "4   535.0  23.573       0       0    0        1\n", "..    ...     ...     ...     ...  ...      ...\n", "95  753.0  22.728       0       0    1        1\n", "96   -9.0  82.860       0       0    0        3\n", "97    5.0  41.812       0       0    0        0\n", "98   23.0  38.323       0       0    0        0\n", "99   37.0  28.080       0       0    0        2\n", "\n", "[100 rows x 6 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_b2c.head(100)"]}, {"cell_type": "code", "execution_count": 7, "id": "fbb7f450", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"4\" halign=\"left\">DLMP</th>\n", "      <th colspan=\"4\" halign=\"left\">ARPU</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>min</th>\n", "      <th>max</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>max</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-37.0</td>\n", "      <td>215.0</td>\n", "      <td>27.559891</td>\n", "      <td>32.197168</td>\n", "      <td>36.527</td>\n", "      <td>64.980</td>\n", "      <td>47.783924</td>\n", "      <td>6.464546</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>423.0</td>\n", "      <td>830.0</td>\n", "      <td>626.011678</td>\n", "      <td>106.589491</td>\n", "      <td>0.185</td>\n", "      <td>101.618</td>\n", "      <td>42.860019</td>\n", "      <td>18.978365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-40.0</td>\n", "      <td>272.0</td>\n", "      <td>31.759711</td>\n", "      <td>39.247080</td>\n", "      <td>0.000</td>\n", "      <td>36.897</td>\n", "      <td>25.486035</td>\n", "      <td>10.209366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-34.0</td>\n", "      <td>551.0</td>\n", "      <td>69.658765</td>\n", "      <td>100.305927</td>\n", "      <td>61.000</td>\n", "      <td>101.619</td>\n", "      <td>79.708773</td>\n", "      <td>10.345542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>149.0</td>\n", "      <td>506.0</td>\n", "      <td>303.257770</td>\n", "      <td>84.018105</td>\n", "      <td>0.440</td>\n", "      <td>75.072</td>\n", "      <td>37.856348</td>\n", "      <td>12.787491</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          DLMP                                   ARPU                      \\\n", "           min    max        mean         std     min      max       mean   \n", "Cluster                                                                     \n", "0        -37.0  215.0   27.559891   32.197168  36.527   64.980  47.783924   \n", "1        423.0  830.0  626.011678  106.589491   0.185  101.618  42.860019   \n", "2        -40.0  272.0   31.759711   39.247080   0.000   36.897  25.486035   \n", "3        -34.0  551.0   69.658765  100.305927  61.000  101.619  79.708773   \n", "4        149.0  506.0  303.257770   84.018105   0.440   75.072  37.856348   \n", "\n", "                    \n", "               std  \n", "Cluster             \n", "0         6.464546  \n", "1        18.978365  \n", "2        10.209366  \n", "3        10.345542  \n", "4        12.787491  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_b2c.groupby('Cluster')[['DLMP', 'ARPU']].agg(['min', 'max', 'mean', 'std'])"]}, {"cell_type": "code", "execution_count": 8, "id": "d918298b", "metadata": {}, "outputs": [], "source": ["df_b2c.rename(columns={'Cluster': 'Score'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "b88eb8eb", "metadata": {}, "outputs": [], "source": ["# Dictionnaire de renommage\n", "mapping_clusters = {\n", "    0: \"Normal\",\n", "    1: \"Blacklisted\",\n", "    2: \"Bon Client\", \n", "    3: \" A risque\",\n", "    4: \" A haut risque\"\n", "}\n", "\n", "# Appliquer le renommage\n", "df_b2c['Score'] = df_b2c['Score'].map(mapping_clusters)"]}, {"cell_type": "code", "execution_count": 10, "id": "345bc2a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"4\" halign=\"left\">DLMP</th>\n", "      <th colspan=\"4\" halign=\"left\">ARPU</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>min</th>\n", "      <th>max</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>max</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Score</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A haut risque</th>\n", "      <td>149.0</td>\n", "      <td>506.0</td>\n", "      <td>303.257770</td>\n", "      <td>84.018105</td>\n", "      <td>0.440</td>\n", "      <td>75.072</td>\n", "      <td>37.856348</td>\n", "      <td>12.787491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>A risque</th>\n", "      <td>-34.0</td>\n", "      <td>551.0</td>\n", "      <td>69.658765</td>\n", "      <td>100.305927</td>\n", "      <td>61.000</td>\n", "      <td>101.619</td>\n", "      <td>79.708773</td>\n", "      <td>10.345542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Blacklisted</th>\n", "      <td>423.0</td>\n", "      <td>830.0</td>\n", "      <td>626.011678</td>\n", "      <td>106.589491</td>\n", "      <td>0.185</td>\n", "      <td>101.618</td>\n", "      <td>42.860019</td>\n", "      <td>18.978365</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>-40.0</td>\n", "      <td>272.0</td>\n", "      <td>31.759711</td>\n", "      <td>39.247080</td>\n", "      <td>0.000</td>\n", "      <td>36.897</td>\n", "      <td>25.486035</td>\n", "      <td>10.209366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Normal</th>\n", "      <td>-37.0</td>\n", "      <td>215.0</td>\n", "      <td>27.559891</td>\n", "      <td>32.197168</td>\n", "      <td>36.527</td>\n", "      <td>64.980</td>\n", "      <td>47.783924</td>\n", "      <td>6.464546</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 DLMP                                   ARPU           \\\n", "                  min    max        mean         std     min      max   \n", "Score                                                                   \n", " A haut risque  149.0  506.0  303.257770   84.018105   0.440   75.072   \n", " A risque       -34.0  551.0   69.658765  100.305927  61.000  101.619   \n", "Blacklisted     423.0  830.0  626.011678  106.589491   0.185  101.618   \n", "Bon Client      -40.0  272.0   31.759711   39.247080   0.000   36.897   \n", "Normal          -37.0  215.0   27.559891   32.197168  36.527   64.980   \n", "\n", "                                      \n", "                     mean        std  \n", "Score                                 \n", " A haut risque  37.856348  12.787491  \n", " A risque       79.708773  10.345542  \n", "Blacklisted     42.860019  18.978365  \n", "Bon Client      25.486035  10.209366  \n", "Normal          47.783924   6.464546  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_b2c.groupby('Score')[['DLMP', 'ARPU']].agg(['min', 'max', 'mean', 'std'])"]}, {"cell_type": "code", "execution_count": 11, "id": "9afeb20d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.004324 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 261\n", "[LightGBM] [Info] Number of data points in the train set: 940210, number of used features: 4\n", "[LightGBM] [Info] Start training from score -0.860224\n", "[LightGBM] [Info] Start training from score -2.715620\n", "[LightGBM] [Info] Start training from score -1.227160\n", "[LightGBM] [Info] Start training from score -2.076487\n", "[LightGBM] [Info] Start training from score -2.382981\n", "✅ Modèle LightGBM sauvegardé sous 'model_B2C.pkl'\n"]}], "source": ["import joblib\n", "from lightgbm import LGBMClassifier\n", "\n", "# Supposons que tu as entraîné un modèle :\n", "model = LGBMClassifier()\n", "model.fit(X_train, y_train)\n", "\n", "# Sauvegarde du modèle\n", "joblib.dump(model, \"model_B2C.pkl\")\n", "print(\"✅ Modèle LightGBM sauvegardé sous 'model_B2C.pkl'\")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2333df08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Prédictions sauvegardées dans 'model_B2C.csv'\n"]}], "source": ["import pandas as pd\n", "\n", "# Prédictions sur le jeu de test\n", "y_pred = catboost.predict(X_test).flatten()  # déjà utilisé dans ton code\n", "\n", "# C<PERSON>er un DataFrame\n", "df_pred = pd.DataFrame({\n", "    \"True_Label\": y_test,\n", "    \"Predicted_Label\": y_pred\n", "})\n", "\n", "# Sauvegarder dans un CSV\n", "df_pred.to_csv(\"model_b2c.csv\", index=False)\n", "print(\"✅ Prédictions sauvegardées dans 'model_B2C.csv'\")"]}, {"cell_type": "code", "execution_count": null, "id": "459d2a27", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}