<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Connexion | Tunisie Telecom</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"/>
  <style>
    /* Fallback icons if Font Awesome doesn't load */
    .fa-user::before { content: '👤'; }
    .fa-lock::before { content: '🔒'; }
    .fa-spinner::before { content: '⟳'; }
    .fa-check::before { content: '✓'; }
  </style>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Montserrat', Aria<PERSON>, sans-serif;
      background: #181c24;
      overflow-x: hidden;
      overflow-y: auto;
    }

    /* Footer styles */
    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(24,28,36,0.95);
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(0,191,255,0.1);
      padding: 12px 20px;
      text-align: center;
      z-index: 10;
      font-size: 13px;
      color: #8892b0;
      box-shadow: 0 -2px 20px rgba(0,0,0,0.3);
    }

    .footer .brand {
      color: #00bfff;
      font-weight: 700;
      text-shadow: 0 0 10px rgba(0,191,255,0.3);
    }

    .footer .year {
      color: #0099cc;
      font-weight: 600;
    }

    /* Animated particles */
    .particles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 0;
      pointer-events: none;
    }

    .particle {
      position: absolute;
      width: 2px;
      height: 2px;
      background: #00bfff;
      border-radius: 50%;
      opacity: 0.4;
      animation: float 20s infinite linear;
    }

    @keyframes float {
      0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 0.4;
      }
      90% {
        opacity: 0.4;
      }
      100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
      }
    }

    /* Floating orbs/bubbles - much more subtle */
    .orb {
      position: absolute;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(0,191,255,0.05), transparent);
      animation: subtleDrift 30s infinite ease-in-out;
    }

    .bubble {
      position: absolute;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, rgba(0,191,255,0.08), rgba(44,15,248,0.03), transparent);
      animation: subtleDrift 35s infinite ease-in-out;
    }

    .orb:nth-child(1) {
      width: 100px;
      height: 100px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .orb:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 15%;
      animation-delay: -10s;
    }

    .orb:nth-child(3) {
      width: 120px;
      height: 120px;
      bottom: 10%;
      left: 30%;
      animation-delay: -20s;
    }

    @keyframes subtleDrift {
      0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.3;
      }
      50% {
        transform: translate(20px, -15px) scale(1.05);
        opacity: 0.1;
      }
    }

    @keyframes drift {
      0%, 100% {
        transform: translate(0, 0) scale(1) rotate(0deg);
        opacity: 0.6;
      }
      33% {
        transform: translate(30px, -30px) scale(1.1) rotate(120deg);
        opacity: 0.8;
      }
      66% {
        transform: translate(-20px, 20px) scale(0.9) rotate(240deg);
        opacity: 0.4;
      }
    }

    @keyframes bubbleFloat {
      0%, 100% {
        transform: translate(0, 0) scale(1) rotate(0deg);
        opacity: 0.5;
      }
      25% {
        transform: translate(40px, -25px) scale(1.15) rotate(90deg);
        opacity: 0.7;
      }
      50% {
        transform: translate(-15px, -40px) scale(0.85) rotate(180deg);
        opacity: 0.3;
      }
      75% {
        transform: translate(-35px, 15px) scale(1.05) rotate(270deg);
        opacity: 0.6;
      }
    }

    /* Main container layout */
    .main-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
      gap: 60px;
    }

    /* Typewriter section */
    .typewriter-section {
      background: rgba(30,34,44,0.85);
      border-radius: 20px;
      padding: 40px;
      width: 420px;
      backdrop-filter: blur(10px);
      border: 1.5px solid #232a3a;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .typewriter-title {
      color: #00bfff;
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 25px;
      text-align: center;
      text-shadow: 0 0 15px rgba(0,191,255,0.4);
    }

    .typewriter-text {
      font-size: 18px;
      color: #e2e8f0;
      line-height: 1.6;
      min-height: 150px;
      font-weight: 400;
    }

    .typewriter {
      display: inline;
      border-right: 2px solid #00bfff;
      animation: blink 1s infinite;
    }

    @keyframes blink {
      0%, 50% { border-color: #00bfff; }
      51%, 100% { border-color: transparent; }
    }

    #vanta-bg {
      position: fixed;
      top: 50%;
      left: 50%;
      width: 500px;
      height: 500px;
      transform: translate(-50%, -50%);
      z-index: 0;
      opacity: 0.5;
      pointer-events: none;
    }

    .login-container {
      position: relative;
      z-index: 2;
      background: rgba(30,34,44,0.92);
      border-radius: 22px;
      box-shadow: 0 12px 40px #0099cc33, 0 1.5px 8px #0099cc33;
      width: 370px;
      padding: 44px 36px 36px 36px;
      display: flex;
      flex-direction: column;
      align-items: center;
      backdrop-filter: blur(7px) saturate(1.2);
      border: 1.5px solid #232a3a;
      animation: fadeInUp 1.2s cubic-bezier(.4,0,.2,1);
      transform-origin: center;
    }

    @keyframes fadeInUp {
      from { 
        opacity: 0; 
        transform: translateY(60px) scale(0.9);
        filter: blur(5px);
      }
      to { 
        opacity: 1; 
        transform: translateY(0) scale(1);
        filter: blur(0px);
      }
    }

    .login-container:hover {
      transform: translateY(-3px);
      box-shadow: 0 20px 60px #0099cc44, 0 3px 12px #0099cc44;
      transition: all 0.3s ease;
    }

    .logo {
      width: 100px;
      height: 80px;
      border-radius: 50%;;
      margin-bottom: 12px;
      filter: drop-shadow(0 2px 8px #0099cc33);
      animation: logoFloat 3s ease-in-out infinite alternate;
    }

   

    h2 {
      margin: 0 0 10px 0;
      font-size: 26px;
      color: #fff;
      font-weight: 900;
      letter-spacing: 1px;
      text-align: center;
      animation: titleGlow 2s ease-in-out infinite alternate;
    }

    @keyframes titleGlow {
      from {
        text-shadow: 0 0 20px rgba(0,191,255,0.3);
      }
      to {
        text-shadow: 0 0 30px rgba(0,191,255,0.6), 0 0 40px rgba(0,191,255,0.2);
      }
    }

    .subtitle {
      color: #00bfff;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 22px;
      text-align: center;
      animation: subtitlePulse 3s ease-in-out infinite;
    }

    @keyframes subtitlePulse {
      0%, 100% {
        opacity: 0.8;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.02);
      }
    }

    .login-form {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 18px;
    }

    .input-group {
      position: relative;
      display: flex;
      align-items: center;
      animation: slideInLeft 0.8s ease-out forwards;
      opacity: 0;
      transform: translateX(-50px);
    }

    .input-group:nth-child(1) {
      animation-delay: 0.3s;
    }

    .input-group:nth-child(2) {
      animation-delay: 0.5s;
    }

    @keyframes slideInLeft {
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .input-group i {
      position: absolute;
      left: 12px;
      color: #00bfff;
      font-size: 17px;
      opacity: 0.7;
      transition: all 0.3s ease;
      animation: iconBounce 2s ease-in-out infinite;
    }

    @keyframes iconBounce {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
    }

    .input-group:focus-within i {
      color: #00bfff;
      opacity: 1;
      transform: scale(1.2);
      animation: none;
    }

    .login-form input {
      padding: 12px 12px 12px 38px;
      border-radius: 10px;
      border: 1.2px solid #232a3a;
      font-size: 16px;
      background: #232a3a;
      color: #fff;
      transition: all 0.3s ease;
      width: 100%;
      position: relative;
    }

    .login-form input:focus {
      border: 1.5px solid #00bfff;
      outline: none;
      background: rgba(35,42,58,0.8);
      box-shadow: 0 0 20px rgba(0,191,255,0.2);
      transform: scale(1.02);
    }

    .login-form input:focus::placeholder {
      color: transparent;
    }

    .login-form button {
      padding: 13px;
      border-radius: 12px;
      border: none;
      background: linear-gradient(90deg, #0099cc 0%, #004080 100%);
      color: #fff;
      font-size: 18px;
      font-weight: 800;
      cursor: pointer;
      box-shadow: 0 4px 16px #0099cc33;
      letter-spacing: 1px;
      margin-top: 8px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      animation: slideInUp 0.8s ease-out 0.7s forwards;
      opacity: 0;
      transform: translateY(30px);
    }

    @keyframes slideInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .login-form button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .login-form button:hover::before {
      left: 100%;
    }

    .login-form button:hover {
      background: linear-gradient(90deg, #004080 0%, #0099cc 100%);
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 35px #0099cc55;
    }

    .login-form button:active {
      transform: translateY(-1px) scale(1.02);
    }

    .error-msg {
      color: #e74c3c;
      font-size: 14px;
      margin-top: 8px;
      text-align: center;
      min-height: 18px;
      animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    .login-anim-left, .login-anim-right {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      pointer-events: none;
      animation: sideAnimation 4s ease-in-out infinite alternate;
    }

    @keyframes sideAnimation {
      0% {
        transform: translateY(-50%) scale(1);
        opacity: 0.6;
      }
      100% {
        transform: translateY(-55%) scale(1.05);
        opacity: 0.9;
      }
    }

    .login-anim-left {
      left: calc(50% - 270px);
      display: block;
    }
    .login-anim-right {
      right: calc(50% - 270px);
      display: block;
    }

    @media (max-width: 1200px) {
      .main-container {
        flex-direction: column;
        gap: 40px;
      }
      .typewriter-section {
        width: 100%;
        max-width: 420px;
      }
    }

    @media (max-width: 900px) {
      .login-anim-left, .login-anim-right { display: none; }
    }

    @media (max-width: 500px) {
      .login-container { 
        width: 95vw; 
        padding: 24px 8vw;
      }
      .typewriter-section {
        width: 95vw;
        padding: 24px 8vw;
      }
    }

    .wave-anim {
      stroke-dasharray: 400;
      stroke-dashoffset: 400;
      animation: waveDraw 2.5s cubic-bezier(.4,0,.2,1) forwards, wavePulse 3s 2.5s infinite alternate;
    }
    @keyframes waveDraw {
      to { stroke-dashoffset: 0; }
    }
    @keyframes wavePulse {
      0% { 
        filter: drop-shadow(0 0 0 #00bfff);
        stroke-width: 3;
      }
      100% { 
        filter: drop-shadow(0 0 12px #00bfff);
        stroke-width: 4;
      }
    }
    .node-anim {
      opacity: 0;
      animation: nodePop 0.8s ease-out forwards, nodeGlow 2s 3s ease-in-out infinite alternate;
    }
    .node-anim:nth-child(2) { animation-delay: 1.2s, 4.2s; }
    .node-anim:nth-child(3) { animation-delay: 1.7s, 4.7s; }
    .node-anim:nth-child(4) { animation-delay: 2.2s, 5.2s; }
    
    @keyframes nodePop {
      to { 
        opacity: 1; 
        transform: scale(1.2);
      }
    }

    @keyframes nodeGlow {
      0% {
        filter: drop-shadow(0 0 5px currentColor);
        transform: scale(1.2);
      }
      100% {
        filter: drop-shadow(0 0 15px currentColor);
        transform: scale(1.4);
      }
    }

    /* Success animation */
    @keyframes successPulse {
      0% {
        background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
        box-shadow: 0 4px 16px rgba(39, 174, 96, 0.4);
      }
      50% {
        background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
        box-shadow: 0 8px 32px rgba(46, 204, 113, 0.6);
      }
      100% {
        background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
        box-shadow: 0 4px 16px rgba(39, 174, 96, 0.4);
      }
    }

    .success {
      animation: successPulse 0.6s ease-in-out;
    }
  </style>
</head>
<body>
  <!-- Animated particles -->
  <div class="particles" id="particles"></div>
  
  <!-- Subtle floating orbs -->
  <div class="orb"></div>
  <div class="orb"></div>
  <div class="orb"></div>

  <div class="svg-bg">
    <div class="login-anim-left">
      <svg width="90" height="320" viewBox="0 0 90 320" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path class="wave-anim" d="M10 10 Q60 80 10 150 Q60 220 10 290" stroke="#0099cc" stroke-width="3" fill="none"/>
        <circle class="node-anim" cx="10" cy="10" r="6" fill="#2c0ff8"/>
        <circle class="node-anim" cx="10" cy="150" r="6" fill="#00bfff"/>
        <circle class="node-anim" cx="10" cy="290" r="6" fill="#0099cc"/>
      </svg>
    </div>
    
    <!-- Telecom dark SVG: antennas, waves, nodes -->
    <svg width="100%" height="100%" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" style="position:absolute;top:0;left:0;width:100vw;height:100vh;z-index:0;">
      <defs>
        <radialGradient id="bgfade" cx="50%" cy="40%" r="80%" fx="50%" fy="40%">
          <stop offset="0%" stop-color="#232a3a" stop-opacity="0.9"/>
          <stop offset="100%" stop-color="#181c24" stop-opacity="1"/>
        </radialGradient>
        <linearGradient id="wave" x1="0" y1="0" x2="1" y2="1">
          <stop offset="0%" stop-color="#0099cc" stop-opacity="0.18"/>
          <stop offset="100%" stop-color="#2c0ff8" stop-opacity="0.12"/>
        </linearGradient>
      </defs>
      <rect width="1920" height="1080" fill="url(#bgfade)"/>
      <!-- Antenna -->
      <g opacity="0.18">
        <rect x="930" y="600" width="12" height="120" rx="6" fill="#00bfff"/>
        <circle cx="936" cy="600" r="18" fill="#00bfff"/>
        <circle cx="936" cy="600" r="38" stroke="#00bfff" stroke-width="2" fill="none">
          <animate attributeName="r" values="38;45;38" dur="3s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="936" cy="600" r="70" stroke="#00bfff" stroke-width="1.5" fill="none">
          <animate attributeName="r" values="70;85;70" dur="4s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;0.2;0.6" dur="4s" repeatCount="indefinite"/>
        </circle>
        <circle cx="936" cy="600" r="120" stroke="#00bfff" stroke-width="1" fill="none">
          <animate attributeName="r" values="120;140;120" dur="5s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.4;0.1;0.4" dur="5s" repeatCount="indefinite"/>
        </circle>
      </g>
      <!-- Network nodes -->
      <g opacity="0.13">
        <circle cx="400" cy="300" r="8" fill="#2c0ff8">
          <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1600" cy="250" r="10" fill="#0099cc">
          <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="300" cy="900" r="7" fill="#0099cc">
          <animate attributeName="opacity" values="0.7;0.2;0.7" dur="3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1700" cy="900" r="9" fill="#2c0ff8">
          <animate attributeName="opacity" values="0.5;0.1;0.5" dur="2.2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1200" cy="200" r="6" fill="#00bfff">
          <animate attributeName="opacity" values="0.8;0.4;0.8" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="700" cy="950" r="5" fill="#00bfff">
          <animate attributeName="opacity" values="0.6;0.3;0.6" dur="2.8s" repeatCount="indefinite"/>
        </circle>
      </g>
      <!-- Subtle waves -->
      <path d="M0 800 Q600 700 1200 900 T1920 800" stroke="url(#wave)" stroke-width="32" fill="none">
        <animate attributeName="d" values="M0 800 Q600 700 1200 900 T1920 800;M0 820 Q600 720 1200 880 T1920 820;M0 800 Q600 700 1200 900 T1920 800" dur="8s" repeatCount="indefinite"/>
      </path>
      <path d="M0 900 Q800 1000 1920 850" stroke="url(#wave)" stroke-width="18" fill="none">
        <animate attributeName="d" values="M0 900 Q800 1000 1920 850;M0 880 Q800 980 1920 870;M0 900 Q800 1000 1920 850" dur="6s" repeatCount="indefinite"/>
      </path>
    </svg>
  </div>
  
  <div class="main-container">
    <!-- Typewriter Section -->
    <div class="typewriter-section">
      <h3 class="typewriter-title"> Votre Assistant à l'aide à la Décision Scoring Client</h3>
      <div class="typewriter-text">
        <span id="typewriter" class="typewriter"></span>
      </div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
      <img src="static/logo.png" alt="Tunisie Telecom Logo" class="logo" />
      <h2>Connexion</h2>
      <div class="subtitle">Accès sécurisé à la plateforme Tunisie Telecom</div>
  <form class="login-form" id="loginForm" autocomplete="off" method="POST" action="/login">
        <div class="input-group">
          <i class="fa fa-user"></i>
          <input type="text" id="username" name="username" placeholder="Nom d'utilisateur" required />
        </div>
        <div class="input-group">
          <i class="fa fa-lock"></i>
          <input type="password" id="password" name="password" placeholder="Mot de passe" required />
        </div>
        <button type="submit">Se connecter</button>
        {% if error %}
          <div class="error-msg" id="errorMsg">{{ error }}</div>
        {% else %}
          <div class="error-msg" id="errorMsg"></div>
        {% endif %}
      </form>
    </div>
  </div>
  <div class="login-anim-right">
    <svg width="90" height="320" viewBox="0 0 90 320" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path class="wave-anim" d="M80 10 Q30 80 80 150 Q30 220 80 290" stroke="#2c0ff8" stroke-width="3" fill="none"/>
      <circle class="node-anim" cx="80" cy="10" r="6" fill="#0099cc"/>
      <circle class="node-anim" cx="80" cy="150" r="6" fill="#00bfff"/>
      <circle class="node-anim" cx="80" cy="290" r="6" fill="#2c0ff8"/>
    </svg>
  </div>

  <!-- Footer OUTSIDE the card and animation -->
  <div class="footer">
    <span class="brand">© Réalisé par Eya Mrad dans le cadre d'un STAGE à TUNISIE TELECOM</span> <span class="year">2025</span> | SCORING CLIENT | Tous droits réservés ©
  </div>

  <script>
    //  floating particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      if (!particlesContainer) return;
      const particleCount = 30;
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        const colors = ['#00bfff', '#0099cc', '#2c0ff8'];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        particle.style.boxShadow = `0 0 6px ${particle.style.background}`;
        particlesContainer.appendChild(particle);
      }
    }

    // Typewriter effect
    const texts = [
      "Votre guide intelligent à l'aide à la décision...",
      "Analyse prédictive des comportements clients...",
      "Scoring automatisé et personnalisé...",
      "Intelligence artificielle au service de Tunisie Telecom...",
      "Optimisation des stratégies commerciales...",
      "Détection des opportunités business..."
    ];
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    function typeWriter() {
      const currentText = texts[textIndex];
      const typewriterElement = document.getElementById('typewriter');
      if (!isDeleting) {
        typewriterElement.textContent = currentText.substring(0, charIndex + 1);
        charIndex++;
        if (charIndex === currentText.length) {
          setTimeout(() => { isDeleting = true; }, 2000);
        }
      } else {
        typewriterElement.textContent = currentText.substring(0, charIndex);
        charIndex--;
        if (charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
        }
      }
      const speed = isDeleting ? 50 : 100;
      setTimeout(typeWriter, speed);
    }

    // Initialize typewriter and particles when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        createParticles();
        setTimeout(typeWriter, 1000);
      });
    } else {
      createParticles();
      setTimeout(typeWriter, 1000);
    }
  </script>

</body>
</html>