from flask import Flask, render_template, request, jsonify, redirect, url_for, make_response
import joblib
import numpy as np
from catboost import CatBoostClassifier

app = Flask(__name__)

# Charger le modèle B2C (LightGBM)
model_b2c = joblib.load("model_B2C.pkl")

# Charger le modèle B2B (CatBoost)
model_b2b = CatBoostClassifier()
model_b2b.load_model("catboost_model.pkl")

# -------------------
# Routes principales
# -------------------

@app.route('/', methods=['GET'])
def home():
    """Page par défaut : login"""
    return render_template('login.html')


@app.route('/login', methods=['GET', 'POST'])
def login_page():
    """Page de login accessible même si déjà connecté"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if username == 'admin' and password == 'telecom2025':
            resp = make_response(redirect(url_for('landing')))
            resp.set_cookie('logged_in', '1', max_age=86400, path='/')
            return resp
        # Retour login avec erreur
        return render_template('login.html', error="Identifiants invalides")
    # GET : affiche la page login
    return render_template('login.html')


@app.route('/landing', methods=['GET'])
def landing():
    """Page après connexion"""
    return render_template('landing_page.html')


# -------------------
# Routes de prédiction
# -------------------

@app.route("/predict_b2c", methods=["POST"])
def predict_b2c():
    import csv
    data = request.get_json()
    features = np.array([[float(data.get("DLMP", 0)),
                          float(data.get("IMPAYE", 0)),
                          float(data.get("FRAUDE", 0)),
                          float(data.get("CTX", 0))]])
    prediction = model_b2c.predict(features)[0]
    cluster = int(prediction)
    score = cluster + 1
    interpretation_map = {
        1: "Bon client",
        2: "Normal",
        3: "À risque",
        4: "À haut risque",
        5: "Blacklisted (ou à blacklister bientôt)"
    }
    interpretation = interpretation_map.get(score, "Inconnu")
    row = {
        "DLMP": data.get("DLMP", 0),
        "IMPAYE": data.get("IMPAYE", 0),
        "FRAUDE": data.get("FRAUDE", 0),
        "CTX": data.get("CTX", 0),
        "cluster": cluster,
        "score": score,
        "interpretation": interpretation
    }
    try:
        with open("predictions_b2c.csv", "a", newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=row.keys())
            if f.tell() == 0:
                writer.writeheader()
            writer.writerow(row)
    except Exception:
        pass
    return jsonify({"cluster": cluster, "score": score, "interpretation": interpretation})


@app.route("/predict_b2b", methods=["POST"])
def predict_b2b():
    import csv
    data = request.get_json()
    features = np.array([[float(data.get("DLMP", 0)),
                          float(data.get("IMPAYE", 0)),
                          float(data.get("FRAUDE", 0)),
                          float(data.get("CTX", 0))]])
    prediction = model_b2b.predict(features)[0]
    cluster = int(prediction)
    score = cluster + 1
    interpretation_map = {
        1: "Bon client",
        2: "Normal",
        3: "À risque",
        4: "À haut risque",
        5: "Blacklisted (ou à blacklister bientôt)"
    }
    interpretation = interpretation_map.get(score, "Inconnu")
    row = {
        "DLMP": data.get("DLMP", 0),
        "IMPAYE": data.get("IMPAYE", 0),
        "FRAUDE": data.get("FRAUDE", 0),
        "CTX": data.get("CTX", 0),
        "cluster": cluster,
        "score": score,
        "interpretation": interpretation
    }
    try:
        with open("predictions_b2b.csv", "a", newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=row.keys())
            if f.tell() == 0:
                writer.writeheader()
            writer.writerow(row)
    except Exception:
        pass
    return jsonify({"cluster": cluster, "score": score, "interpretation": interpretation})





# -------------------
# Run
# -------------------

if __name__ == "__main__":
    app.run(debug=True)
